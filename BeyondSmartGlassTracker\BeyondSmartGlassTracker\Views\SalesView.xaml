<UserControl x:Class="BeyondSmartGlassTracker.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Sales Dashboard"
                      Style="{StaticResource HeaderText}"/>
            <TextBlock Text="Comprehensive sales analytics, reports, and performance metrics."
                      Style="{StaticResource BodyText}"/>
        </StackPanel>

        <!-- Action Bar -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <DatePicker materialDesign:HintAssist.Hint="From Date"
                               Width="150"
                               Margin="0,0,15,0"/>
                    <DatePicker materialDesign:HintAssist.Hint="To Date"
                               Width="150"
                               Margin="0,0,15,0"/>
                    <ComboBox materialDesign:HintAssist.Hint="Report Type"
                             Width="150"
                             Margin="0,0,15,0">
                        <ComboBoxItem Content="All Reports"/>
                        <ComboBoxItem Content="Monthly Sales"/>
                        <ComboBoxItem Content="Client Analysis"/>
                        <ComboBoxItem Content="Product Performance"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource SecondaryButton}"
                           Margin="0,0,5,0"
                           Command="{Binding ExportToExcelCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Export Excel" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource WarningButton}"
                           Margin="5,0,10,0"
                           Command="{Binding PrintReportCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print Report" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernButton}"
                           Command="{Binding RefreshDataCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Refresh" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Sales Analytics Content -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="ChartLine" Width="64" Height="64" Foreground="{StaticResource PrimaryBlueBrush}"/>
                <TextBlock Text="Sales Analytics" Style="{StaticResource HeaderText}" HorizontalAlignment="Center" Margin="0,20,0,10"/>
                <TextBlock Text="Sales analytics and reporting functionality will be fully implemented here." Style="{StaticResource BodyText}" HorizontalAlignment="Center"/>
                <TextBlock Text="Print and Excel export functionality is ready to use." Style="{StaticResource SmallText}" HorizontalAlignment="Center" Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
