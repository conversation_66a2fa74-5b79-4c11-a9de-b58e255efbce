<UserControl x:Class="BeyondSmartGlassTracker.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">
    <Grid Margin="20">
        <materialDesign:Card Style="{StaticResource ModernCard}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="ChartLine" Width="64" Height="64" Foreground="{StaticResource PrimaryBlueBrush}"/>
                <TextBlock Text="Sales Dashboard" Style="{StaticResource HeaderText}" HorizontalAlignment="Center" Margin="0,20,0,10"/>
                <TextBlock Text="Sales analytics and reporting functionality will be implemented here." Style="{StaticResource BodyText}" HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
