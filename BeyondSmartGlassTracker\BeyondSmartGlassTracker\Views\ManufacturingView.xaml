<UserControl x:Class="BeyondSmartGlassTracker.Views.ManufacturingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Manufacturing Tracker"
                      Style="{StaticResource HeaderText}"/>
            <TextBlock Text="Track manufacturing jobs and production status with detailed reporting."
                      Style="{StaticResource BodyText}"/>
        </StackPanel>

        <!-- Action Bar -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBox materialDesign:HintAssist.Hint="Search manufacturing jobs..."
                            Width="300"
                            Margin="0,0,15,0"/>
                    <ComboBox materialDesign:HintAssist.Hint="Status"
                             Width="150"
                             Margin="0,0,15,0">
                        <ComboBoxItem Content="All Statuses"/>
                        <ComboBoxItem Content="Pending"/>
                        <ComboBoxItem Content="In Production"/>
                        <ComboBoxItem Content="Finished"/>
                        <ComboBoxItem Content="On Hold"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource SecondaryButton}"
                           Margin="0,0,5,0"
                           Command="{Binding ExportToExcelCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Export Excel" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource WarningButton}"
                           Margin="5,0,10,0"
                           Command="{Binding PrintReportCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print Report" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernButton}"
                           Command="{Binding NewJobCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="New Job" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Manufacturing Jobs List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="Factory" Width="64" Height="64" Foreground="{StaticResource PrimaryBlueBrush}"/>
                <TextBlock Text="Manufacturing Jobs" Style="{StaticResource HeaderText}" HorizontalAlignment="Center" Margin="0,20,0,10"/>
                <TextBlock Text="Manufacturing job tracking functionality will be fully implemented here." Style="{StaticResource BodyText}" HorizontalAlignment="Center"/>
                <TextBlock Text="Print and Excel export functionality is ready to use." Style="{StaticResource SmallText}" HorizontalAlignment="Center" Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
