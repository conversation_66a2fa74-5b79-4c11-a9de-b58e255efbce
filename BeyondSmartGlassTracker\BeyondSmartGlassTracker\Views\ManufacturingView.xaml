<UserControl x:Class="BeyondSmartGlassTracker.Views.ManufacturingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">
    <Grid Margin="20">
        <materialDesign:Card Style="{StaticResource ModernCard}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="Factory" Width="64" Height="64" Foreground="{StaticResource PrimaryBlueBrush}"/>
                <TextBlock Text="Manufacturing Tracker" Style="{StaticResource HeaderText}" HorizontalAlignment="Center" Margin="0,20,0,10"/>
                <TextBlock Text="Manufacturing job tracking functionality will be implemented here." Style="{StaticResource BodyText}" HorizontalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
