using BeyondSmartGlassTracker.Models;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;

namespace BeyondSmartGlassTracker.Services
{
    public class PdfExportService : IPdfExportService
    {
        public async Task<byte[]> GenerateQuotationPdfAsync(Quotation quotation)
        {
            using (var memoryStream = new MemoryStream())
            {
                var document = new Document(PageSize.A4, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, memoryStream);
                
                document.Open();

                // Header
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.BLUE);
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

                // Company Header
                var title = new Paragraph("Beyond Smart Glass Tracker", titleFont);
                title.Alignment = Element.ALIGN_CENTER;
                document.Add(title);

                document.Add(new Paragraph(" "));

                // Quotation Details
                var quotationTitle = new Paragraph($"QUOTATION #{quotation.QuotationNumber}", headerFont);
                quotationTitle.Alignment = Element.ALIGN_CENTER;
                document.Add(quotationTitle);

                document.Add(new Paragraph(" "));

                // Client Information
                document.Add(new Paragraph("CLIENT INFORMATION", headerFont));
                document.Add(new Paragraph($"Name: {quotation.Client.Name}", normalFont));
                document.Add(new Paragraph($"Email: {quotation.Client.Email}", normalFont));
                document.Add(new Paragraph($"Phone: {quotation.Client.Phone}", normalFont));
                document.Add(new Paragraph($"City: {quotation.Client.City}", normalFont));

                document.Add(new Paragraph(" "));

                // Quotation Information
                document.Add(new Paragraph("QUOTATION DETAILS", headerFont));
                document.Add(new Paragraph($"Date: {quotation.CreatedAt:dd/MM/yyyy}", normalFont));
                document.Add(new Paragraph($"Status: {quotation.Status}", normalFont));
                document.Add(new Paragraph($"Sales Rep: {quotation.User.FullName}", normalFont));

                document.Add(new Paragraph(" "));

                // Items Table
                var table = new PdfPTable(5);
                table.WidthPercentage = 100;
                table.SetWidths(new float[] { 3, 1, 1, 2, 2 });

                // Table Headers
                table.AddCell(new PdfPCell(new Phrase("Product", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
                table.AddCell(new PdfPCell(new Phrase("Area (sqm)", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
                table.AddCell(new PdfPCell(new Phrase("Devices", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
                table.AddCell(new PdfPCell(new Phrase("Price/sqm", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });
                table.AddCell(new PdfPCell(new Phrase("Total", headerFont)) { BackgroundColor = BaseColor.LIGHT_GRAY });

                // Table Rows
                foreach (var item in quotation.Items)
                {
                    table.AddCell(new Phrase(item.ProductType.Name, normalFont));
                    table.AddCell(new Phrase(item.AreaSqm.ToString("F2"), normalFont));
                    table.AddCell(new Phrase(item.DeviceCount.ToString(), normalFont));
                    table.AddCell(new Phrase($"${item.PricePerSqm:F2}", normalFont));
                    table.AddCell(new Phrase($"${item.TotalPrice:F2}", normalFont));
                }

                document.Add(table);

                document.Add(new Paragraph(" "));

                // Totals
                document.Add(new Paragraph($"Subtotal: ${quotation.SubTotal:F2}", normalFont));
                if (quotation.DiscountPercentage > 0)
                {
                    document.Add(new Paragraph($"Discount ({quotation.DiscountPercentage}%): -${quotation.DiscountAmount:F2}", normalFont));
                }
                document.Add(new Paragraph($"TOTAL: ${quotation.TotalAmount:F2}", headerFont));

                if (!string.IsNullOrEmpty(quotation.Notes))
                {
                    document.Add(new Paragraph(" "));
                    document.Add(new Paragraph("NOTES", headerFont));
                    document.Add(new Paragraph(quotation.Notes, normalFont));
                }

                document.Close();
                return memoryStream.ToArray();
            }
        }

        public async Task<byte[]> GenerateContractPdfAsync(Contract contract)
        {
            using (var memoryStream = new MemoryStream())
            {
                var document = new Document(PageSize.A4, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, memoryStream);
                
                document.Open();

                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.BLUE);
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);

                // Company Header
                var title = new Paragraph("Beyond Smart Glass Tracker", titleFont);
                title.Alignment = Element.ALIGN_CENTER;
                document.Add(title);

                document.Add(new Paragraph(" "));

                // Contract Details
                var contractTitle = new Paragraph($"CONTRACT #{contract.ContractNumber}", headerFont);
                contractTitle.Alignment = Element.ALIGN_CENTER;
                document.Add(contractTitle);

                document.Add(new Paragraph(" "));

                // Client Information
                document.Add(new Paragraph("CLIENT INFORMATION", headerFont));
                document.Add(new Paragraph($"Name: {contract.Client.Name}", normalFont));
                document.Add(new Paragraph($"Email: {contract.Client.Email}", normalFont));
                document.Add(new Paragraph($"Phone: {contract.Client.Phone}", normalFont));
                document.Add(new Paragraph($"City: {contract.Client.City}", normalFont));

                document.Add(new Paragraph(" "));

                // Contract Information
                document.Add(new Paragraph("CONTRACT DETAILS", headerFont));
                document.Add(new Paragraph($"Signed Date: {contract.SignedAt:dd/MM/yyyy}", normalFont));
                document.Add(new Paragraph($"Status: {contract.Status}", normalFont));
                document.Add(new Paragraph($"Total Amount: ${contract.TotalAmount:F2}", normalFont));

                if (!string.IsNullOrEmpty(contract.Terms))
                {
                    document.Add(new Paragraph(" "));
                    document.Add(new Paragraph("TERMS & CONDITIONS", headerFont));
                    document.Add(new Paragraph(contract.Terms, normalFont));
                }

                document.Close();
                return memoryStream.ToArray();
            }
        }

        public async Task<byte[]> GenerateReportPdfAsync(string title, object data)
        {
            using (var memoryStream = new MemoryStream())
            {
                var document = new Document(PageSize.A4, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, memoryStream);
                
                document.Open();

                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.BLUE);
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);

                var reportTitle = new Paragraph(title, titleFont);
                reportTitle.Alignment = Element.ALIGN_CENTER;
                document.Add(reportTitle);

                document.Add(new Paragraph($"Generated on: {DateTime.Now:dd/MM/yyyy HH:mm}", headerFont));

                // Add data content here based on the data object type
                // This is a simplified implementation

                document.Close();
                return memoryStream.ToArray();
            }
        }
    }
}
