<Application x:Class="BeyondSmartGlassTracker.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="LightBlue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary Source="Styles/ButtonStyles.xaml" />
                <ResourceDictionary Source="Styles/TextStyles.xaml" />
                <ResourceDictionary Source="Styles/CardStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Color Palette -->
            <SolidColorBrush x:Key="PrimaryBlueBrush" Color="#4A90E2"/>
            <SolidColorBrush x:Key="LightBlueBrush" Color="#E3F2FD"/>
            <SolidColorBrush x:Key="WhiteBrush" Color="#FFFFFF"/>
            <SolidColorBrush x:Key="LightGrayBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="DarkGrayBrush" Color="#757575"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>

            <!-- Global Font Settings -->
            <FontFamily x:Key="PrimaryFont">Segoe UI</FontFamily>
            <FontFamily x:Key="SecondaryFont">Segoe UI Light</FontFamily>

        </ResourceDictionary>
    </Application.Resources>
</Application>
