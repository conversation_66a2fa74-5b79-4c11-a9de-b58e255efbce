using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class ManufacturingViewModel : BaseViewModel
    {
        private readonly IManufacturingService _manufacturingService;
        private readonly IExcelExportService _excelExportService;
        private readonly IPrintService _printService;

        public ManufacturingViewModel(
            IManufacturingService manufacturingService,
            IExcelExportService excelExportService,
            IPrintService printService)
        {
            _manufacturingService = manufacturingService;
            _excelExportService = excelExportService;
            _printService = printService;
            ManufacturingJobs = new ObservableCollection<ManufacturingJob>();

            NewJobCommand = new RelayCommand(() => NewJob());
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());
            PrintReportCommand = new RelayCommand(async () => await PrintReport());

            LoadManufacturingJobs();
        }

        public ObservableCollection<ManufacturingJob> ManufacturingJobs { get; }

        public ICommand NewJobCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand PrintReportCommand { get; }

        private async void LoadManufacturingJobs()
        {
            try
            {
                var jobs = await _manufacturingService.GetAllJobsAsync();
                ManufacturingJobs.Clear();
                foreach (var job in jobs)
                {
                    ManufacturingJobs.Add(job);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading manufacturing jobs: {ex.Message}");
            }
        }

        private void NewJob()
        {
            System.Diagnostics.Debug.WriteLine("New Manufacturing Job clicked");
        }

        private async Task ExportToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportManufacturingJobsToExcelAsync(ManufacturingJobs);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Manufacturing_Jobs");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Manufacturing jobs exported to Excel successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
            }
        }

        private async Task PrintReport()
        {
            try
            {
                var success = await _printService.PrintManufacturingReportAsync(ManufacturingJobs, "Manufacturing Report");
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Manufacturing report printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing report: {ex.Message}");
            }
        }
    }
}
