<UserControl x:Class="BeyondSmartGlassTracker.Views.QuotationsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Quotations Management"
                      Style="{StaticResource HeaderText}"/>
            <TextBlock Text="Create, manage, and track quotations for smart glass projects."
                      Style="{StaticResource BodyText}"/>
        </StackPanel>

        <!-- Action Bar -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBox materialDesign:HintAssist.Hint="Search quotations..."
                            Width="300"
                            Margin="0,0,15,0"/>
                    <ComboBox materialDesign:HintAssist.Hint="Status"
                             Width="150"
                             Margin="0,0,15,0">
                        <ComboBoxItem Content="All Statuses"/>
                        <ComboBoxItem Content="Draft"/>
                        <ComboBoxItem Content="Sent"/>
                        <ComboBoxItem Content="Approved"/>
                        <ComboBoxItem Content="Rejected"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource SecondaryButton}"
                           Margin="0,0,5,0"
                           Command="{Binding ExportToExcelCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Export Excel" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource WarningButton}"
                           Margin="5,0,5,0"
                           Command="{Binding PrintReportCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print Report" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource SuccessButton}"
                           Margin="5,0,10,0"
                           Command="{Binding PrintWithQRCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Qrcode" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print with QR" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernButton}"
                           Command="{Binding NewQuotationCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="New Quotation" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Quotations List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
            <DataGrid ItemsSource="{Binding Quotations}"
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Quotation #" Binding="{Binding QuotationNumber}" Width="120"/>
                    <DataGridTextColumn Header="Client" Binding="{Binding Client.Name}" Width="200"/>
                    <DataGridTextColumn Header="City" Binding="{Binding Client.City}" Width="120"/>
                    <DataGridTextColumn Header="Total Amount" Binding="{Binding TotalAmount, StringFormat=C}" Width="120"/>
                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                    <DataGridTextColumn Header="Created Date" Binding="{Binding CreatedAt, StringFormat=dd/MM/yyyy}" Width="120"/>
                    <DataGridTemplateColumn Header="Actions" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Style="{StaticResource IconButton}"
                                           ToolTip="View"
                                           Command="{Binding DataContext.ViewQuotationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}">
                                        <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource IconButton}"
                                           ToolTip="Edit"
                                           Command="{Binding DataContext.EditQuotationCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}">
                                        <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource IconButton}"
                                           ToolTip="Export PDF"
                                           Command="{Binding DataContext.ExportPdfCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}">
                                        <materialDesign:PackIcon Kind="FilePdf" Width="16" Height="16"/>
                                    </Button>
                                    <Button Style="{StaticResource IconButton}"
                                           ToolTip="Show QR Code"
                                           Command="{Binding DataContext.ShowQRCodeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                           CommandParameter="{Binding}">
                                        <materialDesign:PackIcon Kind="Qrcode" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
