<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Modern Card Style -->
    <Style x:Key="ModernCard" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{StaticResource WhiteBrush}"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowEdges" Value="All"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Stat Card Style -->
    <Style x:Key="StatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Padding" Value="25"/>
        <Setter Property="MinHeight" Value="120"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="12"/>
                    </Setter.Value>
                </Setter>
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="1.02" ScaleY="1.02"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Primary Stat Card -->
    <Style x:Key="PrimaryStatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource StatCard}">
        <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
    </Style>

    <!-- Success Stat Card -->
    <Style x:Key="SuccessStatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource StatCard}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- Warning Stat Card -->
    <Style x:Key="WarningStatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource StatCard}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
    </Style>

    <!-- Error Stat Card -->
    <Style x:Key="ErrorStatCard" TargetType="materialDesign:Card" BasedOn="{StaticResource StatCard}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
    </Style>

    <!-- List Item Card Style -->
    <Style x:Key="ListItemCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Padding" Value="15"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource LightBlueBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Form Card Style -->
    <Style x:Key="FormCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Padding" Value="30"/>
        <Setter Property="Margin" Value="20"/>
        <Setter Property="MaxWidth" Value="600"/>
    </Style>

    <!-- Chart Card Style -->
    <Style x:Key="ChartCard" TargetType="materialDesign:Card" BasedOn="{StaticResource ModernCard}">
        <Setter Property="Padding" Value="20"/>
        <Setter Property="MinHeight" Value="300"/>
    </Style>

</ResourceDictionary>
