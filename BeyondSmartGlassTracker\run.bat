@echo off
echo ========================================
echo Beyond Smart Glass Tracker
echo ========================================
echo.

REM Check if .NET 8 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 8 is not installed on this system.
    echo.
    echo Please install .NET 8 Desktop Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo Look for ".NET Desktop Runtime 8.0.x" and download the installer.
    echo.
    pause
    exit /b 1
)

echo .NET is installed. Building the application...
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

REM Restore packages
echo Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages.
    pause
    exit /b 1
)

REM Build the application
echo Building the application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed.
    pause
    exit /b 1
)

echo.
echo Build successful! Starting the application...
echo.

REM Run the application
dotnet run --configuration Release

echo.
echo Application closed.
pause
