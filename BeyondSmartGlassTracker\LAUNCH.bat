@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker v2.0 - تشغيل البرنامج

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                      الإصدار 2.0.0                          ║
echo ║                   نظام إدارة المشاريع                        ║
echo ║                  مع رموز QR وطباعة متقدمة                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 اختر طريقة التشغيل:
echo.
echo 1️⃣  تشغيل سريع (موصى به)
echo 2️⃣  تشغيل كامل مع فحص النظام
echo 3️⃣  فحص النظام فقط
echo 4️⃣  تشخيص المشاكل
echo 5️⃣  معلومات النظام
echo 6️⃣  إدارة النسخ الاحتياطية
echo 7️⃣  تنظيف النظام
echo 8️⃣  تحديث البرنامج
echo 9️⃣  وضع المطور
echo 0️⃣  خروج
echo.

set /p choice="اختر رقماً (1-9 أو 0 للخروج): "

if "%choice%"=="1" goto quick_start
if "%choice%"=="2" goto full_start
if "%choice%"=="3" goto check_system
if "%choice%"=="4" goto diagnose
if "%choice%"=="5" goto system_info
if "%choice%"=="6" goto backup_menu
if "%choice%"=="7" goto clean_system
if "%choice%"=="8" goto update_program
if "%choice%"=="9" goto dev_mode
if "%choice%"=="0" goto exit
goto invalid_choice

:quick_start
echo.
echo 🚀 تشغيل سريع...
call RUN-PROGRAM.bat
goto end

:full_start
echo.
echo 🔍 تشغيل كامل مع فحص النظام...
call START.bat
goto end

:check_system
echo.
echo 🔍 فحص النظام...
call CHECK-SYSTEM.bat
goto menu

:diagnose
echo.
echo 🔧 تشخيص المشاكل...
call DIAGNOSE-ISSUES.bat
goto menu

:system_info
echo.
echo 📊 معلومات النظام...
call SYSTEM-INFO.bat
goto menu

:backup_menu
echo.
echo 💾 إدارة النسخ الاحتياطية:
echo.
echo 1. إنشاء نسخة احتياطية
echo 2. استعادة نسخة احتياطية
echo 3. العودة للقائمة الرئيسية
echo.
set /p backup_choice="اختر (1-3): "

if "%backup_choice%"=="1" (
    call BACKUP-DATABASE.bat
    goto menu
)
if "%backup_choice%"=="2" (
    call RESTORE-DATABASE.bat
    goto menu
)
if "%backup_choice%"=="3" goto menu
echo اختيار غير صحيح
pause
goto menu

:clean_system
echo.
echo 🧹 تنظيف النظام...
call CLEAN-SYSTEM.bat
goto menu

:update_program
echo.
echo 🔄 تحديث البرنامج...
call UPDATE-PROGRAM.bat
goto menu

:dev_mode
echo.
echo 👨‍💻 وضع المطور...
call DEV-RUN.bat
goto end

:invalid_choice
echo.
echo ❌ اختيار غير صحيح، يرجى المحاولة مرة أخرى
pause
goto menu

:menu
echo.
echo 🔄 العودة للقائمة الرئيسية؟ (y/N)
set /p return_choice=""
if /i "%return_choice%"=="y" (
    cls
    goto start
)
goto end

:exit
echo.
echo 👋 شكراً لاستخدام Beyond Smart Glass Tracker
echo.
timeout /t 2 >nul
exit /b 0

:end
echo.
echo 📋 نصائح سريعة:
echo ✅ استخدم رموز QR لتتبع المشاريع
echo ✅ اطبع التقارير المفلترة
echo ✅ صدر البيانات إلى Excel
echo ✅ احتفظ بنسخ احتياطية منتظمة
echo.
pause
exit /b 0

:start
goto menu
