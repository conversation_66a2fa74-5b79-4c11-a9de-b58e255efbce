using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class InventoryViewModel : BaseViewModel
    {
        private readonly IInventoryService _inventoryService;
        private readonly IExcelExportService _excelExportService;
        private readonly IPrintService _printService;

        public InventoryViewModel(
            IInventoryService inventoryService,
            IExcelExportService excelExportService,
            IPrintService printService)
        {
            _inventoryService = inventoryService;
            _excelExportService = excelExportService;
            _printService = printService;
            InventoryItems = new ObservableCollection<InventoryItem>();

            NewItemCommand = new RelayCommand(() => NewItem());
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());
            PrintReportCommand = new RelayCommand(async () => await PrintReport());

            LoadInventoryItems();
        }

        public ObservableCollection<InventoryItem> InventoryItems { get; }

        public ICommand NewItemCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand PrintReportCommand { get; }

        private async void LoadInventoryItems()
        {
            try
            {
                var items = await _inventoryService.GetAllItemsAsync();
                InventoryItems.Clear();
                foreach (var item in items)
                {
                    InventoryItems.Add(item);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading inventory items: {ex.Message}");
            }
        }

        private void NewItem()
        {
            System.Diagnostics.Debug.WriteLine("New Inventory Item clicked");
        }

        private async Task ExportToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportInventoryToExcelAsync(InventoryItems);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Inventory");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Inventory exported to Excel successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
            }
        }

        private async Task PrintReport()
        {
            try
            {
                var success = await _printService.PrintInventoryReportAsync(InventoryItems, "Inventory Report");
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Inventory report printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing report: {ex.Message}");
            }
        }
    }
}
