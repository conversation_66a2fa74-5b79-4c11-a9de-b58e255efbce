using BeyondSmartGlassTracker.Data;
using BeyondSmartGlassTracker.Models;
using Microsoft.EntityFrameworkCore;

namespace BeyondSmartGlassTracker.Services
{
    public class QuotationService : IQuotationService
    {
        private readonly AppDbContext _context;
        private readonly IPdfExportService _pdfExportService;

        public QuotationService(AppDbContext context, IPdfExportService pdfExportService)
        {
            _context = context;
            _pdfExportService = pdfExportService;
        }

        public async Task<IEnumerable<Quotation>> GetAllQuotationsAsync()
        {
            return await _context.Quotations
                .Include(q => q.Client)
                .Include(q => q.User)
                .Include(q => q.Items)
                    .ThenInclude(i => i.ProductType)
                .OrderByDescending(q => q.CreatedAt)
                .ToListAsync();
        }

        public async Task<Quotation?> GetQuotationByIdAsync(int id)
        {
            return await _context.Quotations
                .Include(q => q.Client)
                .Include(q => q.User)
                .Include(q => q.Items)
                    .ThenInclude(i => i.ProductType)
                .FirstOrDefaultAsync(q => q.Id == id);
        }

        public async Task<Quotation> CreateQuotationAsync(Quotation quotation)
        {
            quotation.QuotationNumber = await GenerateQuotationNumberAsync();
            quotation.CreatedAt = DateTime.UtcNow;
            
            _context.Quotations.Add(quotation);
            await _context.SaveChangesAsync();
            return quotation;
        }

        public async Task<Quotation> UpdateQuotationAsync(Quotation quotation)
        {
            _context.Quotations.Update(quotation);
            await _context.SaveChangesAsync();
            return quotation;
        }

        public async Task<bool> DeleteQuotationAsync(int id)
        {
            var quotation = await _context.Quotations.FindAsync(id);
            if (quotation == null) return false;

            _context.Quotations.Remove(quotation);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateQuotationNumberAsync()
        {
            var year = DateTime.Now.Year;
            var lastQuotation = await _context.Quotations
                .Where(q => q.QuotationNumber.StartsWith($"Q{year}"))
                .OrderByDescending(q => q.QuotationNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastQuotation != null)
            {
                var numberPart = lastQuotation.QuotationNumber.Substring(5);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"Q{year}{nextNumber:D4}";
        }

        public async Task<byte[]> ExportToPdfAsync(int quotationId)
        {
            var quotation = await GetQuotationByIdAsync(quotationId);
            if (quotation == null)
                throw new ArgumentException("Quotation not found");

            return await _pdfExportService.GenerateQuotationPdfAsync(quotation);
        }
    }

    public class ContractService : IContractService
    {
        private readonly AppDbContext _context;

        public ContractService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Contract>> GetAllContractsAsync()
        {
            return await _context.Contracts
                .Include(c => c.Client)
                .Include(c => c.User)
                .Include(c => c.Quotation)
                .Include(c => c.Items)
                    .ThenInclude(i => i.ProductType)
                .OrderByDescending(c => c.SignedAt)
                .ToListAsync();
        }

        public async Task<Contract?> GetContractByIdAsync(int id)
        {
            return await _context.Contracts
                .Include(c => c.Client)
                .Include(c => c.User)
                .Include(c => c.Quotation)
                .Include(c => c.Items)
                    .ThenInclude(i => i.ProductType)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Contract> CreateContractFromQuotationAsync(int quotationId)
        {
            var quotation = await _context.Quotations
                .Include(q => q.Items)
                    .ThenInclude(i => i.ProductType)
                .FirstOrDefaultAsync(q => q.Id == quotationId);

            if (quotation == null)
                throw new ArgumentException("Quotation not found");

            var contract = new Contract
            {
                ContractNumber = await GenerateContractNumberAsync(),
                QuotationId = quotationId,
                ClientId = quotation.ClientId,
                UserId = quotation.UserId,
                TotalAmount = quotation.TotalAmount,
                SignedAt = DateTime.UtcNow,
                Status = ContractStatus.Active
            };

            // Copy items from quotation
            foreach (var quotationItem in quotation.Items)
            {
                contract.Items.Add(new ContractItem
                {
                    ProductTypeId = quotationItem.ProductTypeId,
                    AreaSqm = quotationItem.AreaSqm,
                    DeviceCount = quotationItem.DeviceCount,
                    PricePerSqm = quotationItem.PricePerSqm,
                    TotalPrice = quotationItem.TotalPrice,
                    Description = quotationItem.Description
                });
            }

            // Update quotation status
            quotation.Status = QuotationStatus.Approved;
            quotation.ApprovedAt = DateTime.UtcNow;

            _context.Contracts.Add(contract);
            await _context.SaveChangesAsync();
            return contract;
        }

        public async Task<Contract> UpdateContractAsync(Contract contract)
        {
            _context.Contracts.Update(contract);
            await _context.SaveChangesAsync();
            return contract;
        }

        public async Task<bool> DeleteContractAsync(int id)
        {
            var contract = await _context.Contracts.FindAsync(id);
            if (contract == null) return false;

            _context.Contracts.Remove(contract);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateContractNumberAsync()
        {
            var year = DateTime.Now.Year;
            var lastContract = await _context.Contracts
                .Where(c => c.ContractNumber.StartsWith($"C{year}"))
                .OrderByDescending(c => c.ContractNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastContract != null)
            {
                var numberPart = lastContract.ContractNumber.Substring(5);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"C{year}{nextNumber:D4}";
        }
    }
}
