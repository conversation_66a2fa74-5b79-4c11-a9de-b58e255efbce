using System.Windows;
using System.Windows.Controls;
using BeyondSmartGlassTracker.ViewModels;

namespace BeyondSmartGlassTracker.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            DataContext = App.GetService<LoginViewModel>();
            
            // Handle password box separately since it doesn't support binding
            PasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is LoginViewModel vm)
                {
                    vm.Password = PasswordBox.Password;
                }
            };

            // Handle login success
            if (DataContext is LoginViewModel viewModel)
            {
                viewModel.LoginSuccessful += (s, e) =>
                {
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                };
            }
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            UsernameTextBox.Focus();
        }
    }
}
