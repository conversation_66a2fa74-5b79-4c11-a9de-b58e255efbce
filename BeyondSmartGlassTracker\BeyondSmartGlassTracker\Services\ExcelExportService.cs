using BeyondSmartGlassTracker.Models;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Microsoft.Win32;
using System.Drawing;
using System.IO;

namespace BeyondSmartGlassTracker.Services
{
    public class ExcelExportService : IExcelExportService
    {
        public ExcelExportService()
        {
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<byte[]> ExportQuotationsToExcelAsync(IEnumerable<Quotation> quotations, string fileName = "Quotations")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Quotations");

            // Headers
            var headers = new[]
            {
                "Quotation Number", "Client Name", "Client Email", "Client Phone", "Client City",
                "Status", "Sub Total", "Discount %", "Discount Amount", "Total Amount",
                "Created Date", "Approved Date", "Expires Date", "Sales Rep", "Notes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var quotation in quotations)
            {
                worksheet.Cells[row, 1].Value = quotation.QuotationNumber;
                worksheet.Cells[row, 2].Value = quotation.Client?.Name;
                worksheet.Cells[row, 3].Value = quotation.Client?.Email;
                worksheet.Cells[row, 4].Value = quotation.Client?.Phone;
                worksheet.Cells[row, 5].Value = quotation.Client?.City;
                worksheet.Cells[row, 6].Value = quotation.Status.ToString();
                worksheet.Cells[row, 7].Value = quotation.SubTotal;
                worksheet.Cells[row, 8].Value = quotation.DiscountPercentage;
                worksheet.Cells[row, 9].Value = quotation.DiscountAmount;
                worksheet.Cells[row, 10].Value = quotation.TotalAmount;
                worksheet.Cells[row, 11].Value = quotation.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 12].Value = quotation.ApprovedAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 13].Value = quotation.ExpiresAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 14].Value = quotation.User?.FullName;
                worksheet.Cells[row, 15].Value = quotation.Notes;

                // Format currency columns
                worksheet.Cells[row, 7].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[row, 9].Style.Numberformat.Format = "$#,##0.00";
                worksheet.Cells[row, 10].Style.Numberformat.Format = "$#,##0.00";

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportContractsToExcelAsync(IEnumerable<Contract> contracts, string fileName = "Contracts")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Contracts");

            // Headers
            var headers = new[]
            {
                "Contract Number", "Client Name", "Client Email", "Client Phone", "Client City",
                "Status", "Total Amount", "Signed Date", "Start Date", "Completion Date",
                "Sales Rep", "Terms", "Notes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var contract in contracts)
            {
                worksheet.Cells[row, 1].Value = contract.ContractNumber;
                worksheet.Cells[row, 2].Value = contract.Client?.Name;
                worksheet.Cells[row, 3].Value = contract.Client?.Email;
                worksheet.Cells[row, 4].Value = contract.Client?.Phone;
                worksheet.Cells[row, 5].Value = contract.Client?.City;
                worksheet.Cells[row, 6].Value = contract.Status.ToString();
                worksheet.Cells[row, 7].Value = contract.TotalAmount;
                worksheet.Cells[row, 8].Value = contract.SignedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 9].Value = contract.StartDate?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 10].Value = contract.CompletionDate?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 11].Value = contract.User?.FullName;
                worksheet.Cells[row, 12].Value = contract.Terms;
                worksheet.Cells[row, 13].Value = contract.Notes;

                // Format currency column
                worksheet.Cells[row, 7].Style.Numberformat.Format = "$#,##0.00";

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportManufacturingJobsToExcelAsync(IEnumerable<ManufacturingJob> jobs, string fileName = "Manufacturing_Jobs")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Manufacturing Jobs");

            // Headers
            var headers = new[]
            {
                "Job Number", "Contract Number", "Client Name", "Status", "Priority",
                "Created Date", "Started Date", "Completed Date", "Estimated Completion",
                "Notes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.Orange);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var job in jobs)
            {
                worksheet.Cells[row, 1].Value = job.JobNumber;
                worksheet.Cells[row, 2].Value = job.Contract?.ContractNumber;
                worksheet.Cells[row, 3].Value = job.Contract?.Client?.Name;
                worksheet.Cells[row, 4].Value = job.Status.ToString();
                worksheet.Cells[row, 5].Value = GetPriorityText(job.Priority);
                worksheet.Cells[row, 6].Value = job.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 7].Value = job.StartedAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 8].Value = job.CompletedAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 9].Value = job.EstimatedCompletionDate?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 10].Value = job.Notes;

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportInstallationsToExcelAsync(IEnumerable<Installation> installations, string fileName = "Installations")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Installations");

            // Headers
            var headers = new[]
            {
                "Installation Number", "Contract Number", "Client Name", "Team Name",
                "Status", "Scheduled Date", "Started Date", "Completed Date",
                "Rating", "Client Feedback", "Notes"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightCyan);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var installation in installations)
            {
                worksheet.Cells[row, 1].Value = installation.InstallationNumber;
                worksheet.Cells[row, 2].Value = installation.Contract?.ContractNumber;
                worksheet.Cells[row, 3].Value = installation.Contract?.Client?.Name;
                worksheet.Cells[row, 4].Value = installation.Team?.Name;
                worksheet.Cells[row, 5].Value = installation.Status.ToString();
                worksheet.Cells[row, 6].Value = installation.ScheduledDate.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 7].Value = installation.StartedAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 8].Value = installation.CompletedAt?.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 9].Value = installation.Rating?.ToString();
                worksheet.Cells[row, 10].Value = installation.ClientFeedback;
                worksheet.Cells[row, 11].Value = installation.Notes;

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportInventoryToExcelAsync(IEnumerable<InventoryItem> items, string fileName = "Inventory")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Inventory");

            // Headers
            var headers = new[]
            {
                "SKU", "Name", "Category", "Description", "Current Stock", "Minimum Stock",
                "Maximum Stock", "Unit Cost", "Unit", "Supplier", "Created Date", "Last Updated"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightYellow);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var item in items)
            {
                worksheet.Cells[row, 1].Value = item.SKU;
                worksheet.Cells[row, 2].Value = item.Name;
                worksheet.Cells[row, 3].Value = item.Category.ToString();
                worksheet.Cells[row, 4].Value = item.Description;
                worksheet.Cells[row, 5].Value = item.CurrentStock;
                worksheet.Cells[row, 6].Value = item.MinimumStock;
                worksheet.Cells[row, 7].Value = item.MaximumStock;
                worksheet.Cells[row, 8].Value = item.UnitCost;
                worksheet.Cells[row, 9].Value = item.Unit;
                worksheet.Cells[row, 10].Value = item.Supplier;
                worksheet.Cells[row, 11].Value = item.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 12].Value = item.LastUpdated?.ToString("dd/MM/yyyy");

                // Format currency column
                worksheet.Cells[row, 8].Style.Numberformat.Format = "$#,##0.00";

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportClientsToExcelAsync(IEnumerable<Client> clients, string fileName = "Clients")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Clients");

            // Headers
            var headers = new[] { "Name", "Email", "Phone", "City", "Address", "Created Date", "Notes" };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightPink);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var client in clients)
            {
                worksheet.Cells[row, 1].Value = client.Name;
                worksheet.Cells[row, 2].Value = client.Email;
                worksheet.Cells[row, 3].Value = client.Phone;
                worksheet.Cells[row, 4].Value = client.City;
                worksheet.Cells[row, 5].Value = client.Address;
                worksheet.Cells[row, 6].Value = client.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 7].Value = client.Notes;

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportUsersToExcelAsync(IEnumerable<User> users, string fileName = "Users")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Users");

            // Headers
            var headers = new[] { "Username", "Full Name", "Email", "Role", "Is Active", "Created Date", "Last Login" };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
            }

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, headers.Length])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
            }

            // Data
            int row = 2;
            foreach (var user in users)
            {
                worksheet.Cells[row, 1].Value = user.Username;
                worksheet.Cells[row, 2].Value = user.FullName;
                worksheet.Cells[row, 3].Value = user.Email;
                worksheet.Cells[row, 4].Value = user.Role.ToString();
                worksheet.Cells[row, 5].Value = user.IsActive ? "Yes" : "No";
                worksheet.Cells[row, 6].Value = user.CreatedAt.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 7].Value = user.LastLoginAt?.ToString("dd/MM/yyyy HH:mm");

                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<byte[]> ExportSalesReportToExcelAsync(object salesData, string fileName = "Sales_Report")
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Sales Report");

            // This is a simplified implementation - you would customize based on your sales data structure
            worksheet.Cells[1, 1].Value = "Sales Report";
            worksheet.Cells[1, 1].Style.Font.Bold = true;
            worksheet.Cells[1, 1].Style.Font.Size = 16;

            worksheet.Cells[3, 1].Value = "Generated on:";
            worksheet.Cells[3, 2].Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm");

            return await Task.FromResult(package.GetAsByteArray());
        }

        public async Task<bool> SaveExcelFileAsync(byte[] excelData, string defaultFileName)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"{defaultFileName}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    await File.WriteAllBytesAsync(saveFileDialog.FileName, excelData);
                    return true;
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private static string GetPriorityText(int priority)
        {
            return priority switch
            {
                1 => "Low",
                2 => "Medium",
                3 => "High",
                _ => "Unknown"
            };
        }
    }
}
