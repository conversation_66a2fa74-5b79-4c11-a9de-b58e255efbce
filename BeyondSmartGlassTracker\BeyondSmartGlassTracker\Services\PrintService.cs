using BeyondSmartGlassTracker.Models;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace BeyondSmartGlassTracker.Services
{
    public class PrintService : IPrintService
    {
        private readonly IQRCodeService _qrCodeService;

        public PrintService(IQRCodeService qrCodeService)
        {
            _qrCodeService = qrCodeService;
        }

        public async Task<bool> PrintQuotationsReportAsync(IEnumerable<Quotation> quotations, string title = "Quotations Report")
        {
            try
            {
                var document = CreateQuotationsDocument(quotations, title);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing quotations report: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintContractsReportAsync(IEnumerable<Contract> contracts, string title = "Contracts Report")
        {
            try
            {
                var document = CreateContractsDocument(contracts, title);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing contracts report: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintManufacturingReportAsync(IEnumerable<ManufacturingJob> jobs, string title = "Manufacturing Report")
        {
            try
            {
                var document = CreateManufacturingDocument(jobs, title);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing manufacturing report: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintInventoryReportAsync(IEnumerable<InventoryItem> items, string title = "Inventory Report")
        {
            try
            {
                var document = CreateInventoryDocument(items, title);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing inventory report: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintSalesReportAsync(object salesData, string title = "Sales Report")
        {
            try
            {
                var document = CreateSalesDocument(salesData, title);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing sales report: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintQuotationWithQRAsync(Quotation quotation)
        {
            try
            {
                var document = CreateQuotationWithQRDocument(quotation);
                return await PrintDocumentAsync(document, $"Quotation {quotation.QuotationNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing quotation with QR: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintContractWithQRAsync(Contract contract)
        {
            try
            {
                var document = CreateContractWithQRDocument(contract);
                return await PrintDocumentAsync(document, $"Contract {contract.ContractNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing contract with QR: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> PrintFilteredListWithQRAsync<T>(IEnumerable<T> items, string title, Func<T, string> qrTextGenerator)
        {
            try
            {
                var document = CreateFilteredListWithQRDocument(items, title, qrTextGenerator);
                return await PrintDocumentAsync(document, title);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing filtered list with QR: {ex.Message}");
                return false;
            }
        }

        private FlowDocument CreateQuotationsDocument(IEnumerable<Quotation> quotations, string title)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // Header
            var header = new Paragraph(new Run(title))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Company info
            var companyInfo = new Paragraph(new Run("Beyond Smart Glass Tracker"))
            {
                FontSize = 14,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(companyInfo);

            var dateInfo = new Paragraph(new Run($"Generated on: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // Table
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // Define columns
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Quotation #
            table.Columns.Add(new TableColumn { Width = new GridLength(150) }); // Client
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // City
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Amount
            table.Columns.Add(new TableColumn { Width = new GridLength(80) });  // Status
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Date

            // Header row
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Quotation #"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Client"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("City"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Amount"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Status"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Date"))) { Padding = new Thickness(5) });

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // Data rows
            var dataRowGroup = new TableRowGroup();
            foreach (var quotation in quotations)
            {
                var row = new TableRow();
                
                row.Cells.Add(new TableCell(new Paragraph(new Run(quotation.QuotationNumber))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(quotation.Client?.Name ?? ""))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(quotation.Client?.City ?? ""))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run($"${quotation.TotalAmount:F2}"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(quotation.Status.ToString()))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(quotation.CreatedAt.ToString("dd/MM/yyyy")))) { Padding = new Thickness(5) });

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);

            // Summary
            var summary = new Paragraph(new Run($"Total Quotations: {quotations.Count()}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 20, 0, 0)
            };
            document.Blocks.Add(summary);

            return document;
        }

        private FlowDocument CreateContractsDocument(IEnumerable<Contract> contracts, string title)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // Header
            var header = new Paragraph(new Run(title))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Company info
            var companyInfo = new Paragraph(new Run("Beyond Smart Glass Tracker"))
            {
                FontSize = 14,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };
            document.Blocks.Add(companyInfo);

            var dateInfo = new Paragraph(new Run($"Generated on: {DateTime.Now:dd/MM/yyyy HH:mm}"))
            {
                FontSize = 12,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(dateInfo);

            // Table
            var table = new Table();
            table.CellSpacing = 0;
            table.BorderBrush = Brushes.Black;
            table.BorderThickness = new Thickness(1);

            // Define columns
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Contract #
            table.Columns.Add(new TableColumn { Width = new GridLength(150) }); // Client
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Amount
            table.Columns.Add(new TableColumn { Width = new GridLength(80) });  // Status
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Signed Date
            table.Columns.Add(new TableColumn { Width = new GridLength(100) }); // Completion

            // Header row
            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();
            headerRow.Background = Brushes.LightGray;

            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Contract #"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Client"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Amount"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Status"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Signed Date"))) { Padding = new Thickness(5) });
            headerRow.Cells.Add(new TableCell(new Paragraph(new Run("Completion"))) { Padding = new Thickness(5) });

            headerRowGroup.Rows.Add(headerRow);
            table.RowGroups.Add(headerRowGroup);

            // Data rows
            var dataRowGroup = new TableRowGroup();
            foreach (var contract in contracts)
            {
                var row = new TableRow();
                
                row.Cells.Add(new TableCell(new Paragraph(new Run(contract.ContractNumber))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(contract.Client?.Name ?? ""))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run($"${contract.TotalAmount:F2}"))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(contract.Status.ToString()))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(contract.SignedAt.ToString("dd/MM/yyyy")))) { Padding = new Thickness(5) });
                row.Cells.Add(new TableCell(new Paragraph(new Run(contract.CompletionDate?.ToString("dd/MM/yyyy") ?? "TBD"))) { Padding = new Thickness(5) });

                dataRowGroup.Rows.Add(row);
            }
            table.RowGroups.Add(dataRowGroup);

            document.Blocks.Add(table);

            // Summary
            var summary = new Paragraph(new Run($"Total Contracts: {contracts.Count()}"))
            {
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 20, 0, 0)
            };
            document.Blocks.Add(summary);

            return document;
        }

        private FlowDocument CreateManufacturingDocument(IEnumerable<ManufacturingJob> jobs, string title)
        {
            // Similar implementation for manufacturing jobs
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            var header = new Paragraph(new Run(title))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Add manufacturing-specific content here
            var placeholder = new Paragraph(new Run("Manufacturing jobs report content will be displayed here."))
            {
                FontSize = 14,
                Margin = new Thickness(0, 20, 0, 0)
            };
            document.Blocks.Add(placeholder);

            return document;
        }

        private FlowDocument CreateInventoryDocument(IEnumerable<InventoryItem> items, string title)
        {
            // Similar implementation for inventory items
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            var header = new Paragraph(new Run(title))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Add inventory-specific content here
            var placeholder = new Paragraph(new Run("Inventory report content will be displayed here."))
            {
                FontSize = 14,
                Margin = new Thickness(0, 20, 0, 0)
            };
            document.Blocks.Add(placeholder);

            return document;
        }

        private FlowDocument CreateSalesDocument(object salesData, string title)
        {
            // Implementation for sales report
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            var header = new Paragraph(new Run(title))
            {
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Add sales-specific content here
            var placeholder = new Paragraph(new Run("Sales report content will be displayed here."))
            {
                FontSize = 14,
                Margin = new Thickness(0, 20, 0, 0)
            };
            document.Blocks.Add(placeholder);

            return document;
        }

        private FlowDocument CreateQuotationWithQRDocument(Quotation quotation)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // Header with QR Code
            var headerTable = new Table();
            headerTable.Columns.Add(new TableColumn { Width = new GridLength(2, GridUnitType.Star) });
            headerTable.Columns.Add(new TableColumn { Width = new GridLength(1, GridUnitType.Star) });

            var headerRowGroup = new TableRowGroup();
            var headerRow = new TableRow();

            // Left side - Quotation info
            var leftCell = new TableCell();
            leftCell.Blocks.Add(new Paragraph(new Run($"QUOTATION #{quotation.QuotationNumber}"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold
            });
            leftCell.Blocks.Add(new Paragraph(new Run($"Client: {quotation.Client?.Name}"))
            {
                FontSize = 14
            });
            leftCell.Blocks.Add(new Paragraph(new Run($"Date: {quotation.CreatedAt:dd/MM/yyyy}"))
            {
                FontSize = 14
            });

            // Right side - QR Code
            var rightCell = new TableCell();
            var qrImage = _qrCodeService.GenerateQuotationQRCode(quotation);
            var qrParagraph = new Paragraph();
            var qrInline = new InlineUIContainer(new Image 
            { 
                Source = qrImage, 
                Width = 100, 
                Height = 100 
            });
            qrParagraph.Inlines.Add(qrInline);
            rightCell.Blocks.Add(qrParagraph);

            headerRow.Cells.Add(leftCell);
            headerRow.Cells.Add(rightCell);
            headerRowGroup.Rows.Add(headerRow);
            headerTable.RowGroups.Add(headerRowGroup);

            document.Blocks.Add(headerTable);

            // Add more quotation details here...

            return document;
        }

        private FlowDocument CreateContractWithQRDocument(Contract contract)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            // Similar to quotation but for contract
            var header = new Paragraph(new Run($"CONTRACT #{contract.ContractNumber}"))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Add QR code and contract details here...

            return document;
        }

        private FlowDocument CreateFilteredListWithQRDocument<T>(IEnumerable<T> items, string title, Func<T, string> qrTextGenerator)
        {
            var document = new FlowDocument();
            document.PagePadding = new Thickness(50);

            var header = new Paragraph(new Run(title))
            {
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                TextAlignment = TextAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            document.Blocks.Add(header);

            // Add filtered list with QR codes here...

            return document;
        }

        private async Task<bool> PrintDocumentAsync(FlowDocument document, string jobName)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // Create a paginator for the document
                    var paginator = ((IDocumentPaginatorSource)document).DocumentPaginator;
                    
                    // Print the document
                    printDialog.PrintDocument(paginator, jobName);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing document: {ex.Message}");
                return false;
            }
        }
    }
}
