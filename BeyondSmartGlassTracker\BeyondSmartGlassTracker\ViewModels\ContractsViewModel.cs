using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class ContractsViewModel : BaseViewModel
    {
        private readonly IContractService _contractService;
        private readonly IExcelExportService _excelExportService;
        private readonly IQRCodeService _qrCodeService;
        private readonly IPrintService _printService;

        public ContractsViewModel(
            IContractService contractService,
            IExcelExportService excelExportService,
            IQRCodeService qrCodeService,
            IPrintService printService)
        {
            _contractService = contractService;
            _excelExportService = excelExportService;
            _qrCodeService = qrCodeService;
            _printService = printService;
            Contracts = new ObservableCollection<Contract>();

            NewContractCommand = new RelayCommand(() => NewContract());
            ViewContractCommand = new RelayCommand<Contract>(contract => ViewContract(contract));
            EditContractCommand = new RelayCommand<Contract>(contract => EditContract(contract));
            ExportPdfCommand = new RelayCommand<Contract>(contract => ExportPdf(contract));
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());
            ShowQRCodeCommand = new RelayCommand<Contract>(contract => ShowQRCode(contract));
            PrintReportCommand = new RelayCommand(async () => await PrintReport());
            PrintWithQRCommand = new RelayCommand(async () => await PrintWithQR());

            LoadContracts();
        }

        public ObservableCollection<Contract> Contracts { get; }

        public ICommand NewContractCommand { get; }
        public ICommand ViewContractCommand { get; }
        public ICommand EditContractCommand { get; }
        public ICommand ExportPdfCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand ShowQRCodeCommand { get; }
        public ICommand PrintReportCommand { get; }
        public ICommand PrintWithQRCommand { get; }

        private async void LoadContracts()
        {
            try
            {
                var contracts = await _contractService.GetAllContractsAsync();
                Contracts.Clear();
                foreach (var contract in contracts)
                {
                    Contracts.Add(contract);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading contracts: {ex.Message}");
            }
        }

        private void NewContract()
        {
            System.Diagnostics.Debug.WriteLine("New Contract clicked");
        }

        private void ViewContract(Contract? contract)
        {
            if (contract != null)
                System.Diagnostics.Debug.WriteLine($"View Contract: {contract.ContractNumber}");
        }

        private void EditContract(Contract? contract)
        {
            if (contract != null)
                System.Diagnostics.Debug.WriteLine($"Edit Contract: {contract.ContractNumber}");
        }

        private async void ExportPdf(Contract? contract)
        {
            if (contract != null)
            {
                try
                {
                    // Implementation for PDF export
                    System.Diagnostics.Debug.WriteLine($"Export PDF for: {contract.ContractNumber}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error exporting PDF: {ex.Message}");
                }
            }
        }

        private async Task ExportToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportContractsToExcelAsync(Contracts);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Contracts");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Contracts exported to Excel successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
            }
        }

        private void ShowQRCode(Contract? contract)
        {
            if (contract != null)
            {
                try
                {
                    var qrWindow = new Views.QRCodeWindow();
                    var qrViewModel = App.GetService<QRCodeDisplayViewModel>();

                    var qrText = _qrCodeService.GenerateContractQRText(contract);
                    qrViewModel.GenerateQRCode(qrText, $"Contract {contract.ContractNumber}",
                        $"Client: {contract.Client?.Name} | Amount: ${contract.TotalAmount:F2}");

                    qrWindow.DataContext = qrViewModel;
                    qrWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error showing QR code: {ex.Message}");
                }
            }
        }

        private async Task PrintReport()
        {
            try
            {
                var success = await _printService.PrintContractsReportAsync(Contracts, "Contracts Report");
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Contracts report printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing report: {ex.Message}");
            }
        }

        private async Task PrintWithQR()
        {
            try
            {
                var activeContracts = Contracts.Where(c => c.Status == ContractStatus.Active || c.Status == ContractStatus.Completed);
                var success = await _printService.PrintFilteredListWithQRAsync(
                    activeContracts,
                    "Contracts with QR Codes",
                    c => _qrCodeService.GenerateContractQRText(c));

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Contracts with QR codes printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print with QR was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing with QR codes: {ex.Message}");
            }
        }
    }
}
