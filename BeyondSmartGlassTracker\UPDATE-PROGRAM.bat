@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - تحديث البرنامج

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                        تحديث البرنامج                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تحديث البرنامج...
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

echo 📦 جاري تحديث المكونات...
dotnet restore --force

if %errorlevel% neq 0 (
    echo ❌ فشل في تحديث المكونات
    pause
    exit /b 1
)

echo ✅ تم تحديث المكونات بنجاح

echo 🧹 جاري تنظيف الملفات القديمة...
dotnet clean

echo 🔨 جاري إعادة بناء البرنامج...
dotnet build --configuration Release

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)

echo ✅ تم تحديث البرنامج بنجاح
echo.
echo 🎉 البرنامج جاهز للاستخدام مع آخر التحديثات
echo.
echo الميزات الجديدة:
echo ✅ رموز QR للعروض والعقود
echo ✅ طباعة التقارير المحسنة
echo ✅ تصدير Excel محسن
echo.
pause
