# Beyond Smart Glass Tracker - Setup and Run Script
# This script will check requirements, install dependencies, and run the application

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Beyond Smart Glass Tracker" -ForegroundColor Yellow
Write-Host "Setup and Run Script" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if .NET 8 is installed
function Test-DotNetInstalled {
    try {
        $dotnetVersion = & dotnet --version 2>$null
        if ($dotnetVersion -and $dotnetVersion.StartsWith("8.")) {
            return $true
        }
        return $false
    }
    catch {
        return $false
    }
}

# Function to download and install .NET 8 Desktop Runtime
function Install-DotNetRuntime {
    Write-Host "Downloading .NET 8 Desktop Runtime..." -ForegroundColor Yellow
    
    $downloadUrl = "https://download.microsoft.com/download/6/0/f/60fc7d8c-1d4d-4b6f-b8b2-6b8f8b8b8b8b/windowsdesktop-runtime-8.0.0-win-x64.exe"
    $installerPath = "$env:TEMP\dotnet-desktop-runtime-8.0-win-x64.exe"
    
    try {
        # Note: In a real scenario, you would use the actual download URL
        Write-Host "Please download .NET 8 Desktop Runtime manually from:" -ForegroundColor Red
        Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
        Write-Host "Look for '.NET Desktop Runtime 8.0.x' and install it." -ForegroundColor Cyan
        Write-Host ""
        Read-Host "Press Enter after installing .NET 8 Desktop Runtime"
    }
    catch {
        Write-Host "Failed to download .NET Runtime. Please install manually." -ForegroundColor Red
        return $false
    }
}

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Note: Running without administrator privileges." -ForegroundColor Yellow
    Write-Host "Some operations might require administrator access." -ForegroundColor Yellow
    Write-Host ""
}

# Check .NET installation
Write-Host "Checking .NET 8 installation..." -ForegroundColor Green

if (Test-DotNetInstalled) {
    Write-Host "✓ .NET 8 is installed and ready!" -ForegroundColor Green
} else {
    Write-Host "✗ .NET 8 is not installed." -ForegroundColor Red
    Write-Host ""
    
    $install = Read-Host "Would you like to install .NET 8 Desktop Runtime? (y/n)"
    if ($install -eq "y" -or $install -eq "Y") {
        Install-DotNetRuntime
        
        # Check again after installation
        if (-not (Test-DotNetInstalled)) {
            Write-Host "Please restart this script after installing .NET 8." -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    } else {
        Write-Host "Cannot proceed without .NET 8. Exiting..." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Navigate to project directory
Write-Host ""
Write-Host "Setting up the application..." -ForegroundColor Green

$projectPath = Join-Path $PSScriptRoot "BeyondSmartGlassTracker"
if (-not (Test-Path $projectPath)) {
    Write-Host "Error: Project directory not found at $projectPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Set-Location $projectPath

# Restore NuGet packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
try {
    & dotnet restore
    if ($LASTEXITCODE -ne 0) {
        throw "Restore failed"
    }
    Write-Host "✓ Packages restored successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to restore packages: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Build the application
Write-Host "Building the application..." -ForegroundColor Yellow
try {
    & dotnet build --configuration Release
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✓ Application built successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Build failed: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Display login information
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "LOGIN CREDENTIALS" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Admin User:" -ForegroundColor White
Write-Host "  Username: admin" -ForegroundColor Green
Write-Host "  Password: admin123" -ForegroundColor Green
Write-Host ""
Write-Host "Sales User:" -ForegroundColor White
Write-Host "  Username: sales1" -ForegroundColor Green
Write-Host "  Password: sales123" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Run the application
Write-Host "Starting Beyond Smart Glass Tracker..." -ForegroundColor Green
Write-Host "The application window should open shortly..." -ForegroundColor Yellow
Write-Host ""

try {
    & dotnet run --configuration Release
} catch {
    Write-Host "Error running the application: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Application closed." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
