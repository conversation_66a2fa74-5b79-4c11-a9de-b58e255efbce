<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Header Text Styles -->
    <Style x:Key="HeaderText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <Style x:Key="SubHeaderText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <Style x:Key="SectionHeaderText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>

    <!-- Body Text Styles -->
    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <Style x:Key="SmallText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
    </Style>

    <Style x:Key="CaptionText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource SecondaryFont}"/>
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
        <Setter Property="Opacity" Value="0.8"/>
    </Style>

    <!-- Status Text Styles -->
    <Style x:Key="SuccessText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style x:Key="WarningText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style x:Key="ErrorText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- Link Text Style -->
    <Style x:Key="LinkText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

    <!-- Number/Currency Text Styles -->
    <Style x:Key="CurrencyText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <Style x:Key="LargeCurrencyText" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
    </Style>

</ResourceDictionary>
