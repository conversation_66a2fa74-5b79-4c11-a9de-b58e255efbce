using BeyondSmartGlassTracker.Models;
using QRCoder;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Media.Imaging;
using Microsoft.Win32;

namespace BeyondSmartGlassTracker.Services
{
    public class QRCodeService : IQRCodeService
    {
        public BitmapImage GenerateQuotationQRCode(Quotation quotation)
        {
            var qrText = GenerateQuotationQRText(quotation);
            return GenerateQRCodeFromText(qrText);
        }

        public BitmapImage GenerateContractQRCode(Contract contract)
        {
            var qrText = GenerateContractQRText(contract);
            return GenerateQRCodeFromText(qrText);
        }

        public BitmapImage GenerateQRCodeFromText(string text)
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                var qrCodeData = qrGenerator.CreateQrCode(text, QRCodeGenerator.ECCLevel.Q);
                
                using var qrCode = new QRCode(qrCodeData);
                using var qrCodeImage = qrCode.GetGraphic(20, Color.Black, Color.White, true);
                
                return ConvertBitmapToBitmapImage(qrCodeImage);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating QR code: {ex.Message}");
                return CreateErrorQRCode();
            }
        }

        public string GenerateQuotationQRText(Quotation quotation)
        {
            var qrData = new
            {
                Type = "QUOTATION",
                ProjectId = quotation.Id,
                QuotationNumber = quotation.QuotationNumber,
                ClientName = quotation.Client?.Name ?? "Unknown Client",
                TotalAmount = quotation.TotalAmount,
                Status = quotation.Status.ToString(),
                CreatedDate = quotation.CreatedAt.ToString("yyyy-MM-dd"),
                ExpiryDate = quotation.ExpiresAt?.ToString("yyyy-MM-dd") ?? "No Expiry",
                SalesRep = quotation.User?.FullName ?? "Unknown",
                Company = "Beyond Smart Glass"
            };

            return System.Text.Json.JsonSerializer.Serialize(qrData, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = false 
            });
        }

        public string GenerateContractQRText(Contract contract)
        {
            var qrData = new
            {
                Type = "CONTRACT",
                ProjectId = contract.Id,
                ContractNumber = contract.ContractNumber,
                ClientName = contract.Client?.Name ?? "Unknown Client",
                TotalAmount = contract.TotalAmount,
                Status = contract.Status.ToString(),
                SignedDate = contract.SignedAt.ToString("yyyy-MM-dd"),
                StartDate = contract.StartDate?.ToString("yyyy-MM-dd") ?? "TBD",
                CompletionDate = contract.CompletionDate?.ToString("yyyy-MM-dd") ?? "TBD",
                SalesRep = contract.User?.FullName ?? "Unknown",
                Company = "Beyond Smart Glass"
            };

            return System.Text.Json.JsonSerializer.Serialize(qrData, new System.Text.Json.JsonSerializerOptions 
            { 
                WriteIndented = false 
            });
        }

        public async Task<bool> SaveQRCodeAsImageAsync(BitmapImage qrCode, string fileName)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "PNG Image (*.png)|*.png|JPEG Image (*.jpg)|*.jpg|Bitmap Image (*.bmp)|*.bmp",
                    DefaultExt = "png",
                    FileName = $"{fileName}_QRCode_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var encoder = GetImageEncoder(saveFileDialog.FileName);
                    
                    using var fileStream = new FileStream(saveFileDialog.FileName, FileMode.Create);
                    encoder.Frames.Add(BitmapFrame.Create(qrCode));
                    encoder.Save(fileStream);
                    
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving QR code: {ex.Message}");
                return false;
            }
        }

        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using var memory = new MemoryStream();
            bitmap.Save(memory, ImageFormat.Png);
            memory.Position = 0;

            var bitmapImage = new BitmapImage();
            bitmapImage.BeginInit();
            bitmapImage.StreamSource = memory;
            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
            bitmapImage.EndInit();
            bitmapImage.Freeze();

            return bitmapImage;
        }

        private BitmapImage CreateErrorQRCode()
        {
            try
            {
                using var qrGenerator = new QRCodeGenerator();
                var qrCodeData = qrGenerator.CreateQrCode("ERROR: Could not generate QR code", QRCodeGenerator.ECCLevel.Q);
                
                using var qrCode = new QRCode(qrCodeData);
                using var qrCodeImage = qrCode.GetGraphic(20, Color.Red, Color.White, true);
                
                return ConvertBitmapToBitmapImage(qrCodeImage);
            }
            catch
            {
                // Return a simple 1x1 pixel image as fallback
                var fallbackImage = new BitmapImage();
                fallbackImage.BeginInit();
                fallbackImage.UriSource = new Uri("pack://application:,,,/Assets/error-qr.png", UriKind.Absolute);
                fallbackImage.EndInit();
                return fallbackImage;
            }
        }

        private BitmapEncoder GetImageEncoder(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLower();
            return extension switch
            {
                ".jpg" or ".jpeg" => new JpegBitmapEncoder(),
                ".bmp" => new BmpBitmapEncoder(),
                _ => new PngBitmapEncoder()
            };
        }
    }
}
