@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - تشغيل البرنامج

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                      نظام إدارة المشاريع                      ║
echo ║                        الإصدار 2.0                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري فحص النظام...

REM Check if .NET is available
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET من:
    echo https://dotnet.microsoft.com/download/dotnet/
    echo.
    echo ابحث عن ".NET Desktop Runtime" وقم بتثبيته
    echo.
    pause
    exit /b 1
)

echo ✅ .NET متوفر ومثبت
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

echo 📦 جاري تحضير المكونات...
dotnet restore --verbosity quiet >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ فشل في تحضير المكونات
    echo يرجى التحقق من الاتصال بالإنترنت
    pause
    exit /b 1
)

echo ✅ تم تحضير المكونات بنجاح

echo 🔨 جاري بناء البرنامج...
dotnet build --configuration Release --verbosity quiet >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    echo يرجى التحقق من الأخطاء
    echo.
    echo جاري عرض تفاصيل الخطأ...
    dotnet build --configuration Release
    pause
    exit /b 1
)

echo ✅ تم بناء البرنامج بنجاح
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    بيانات تسجيل الدخول                       ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ المدير العام:                                                ║
echo ║   اسم المستخدم: admin                                        ║
echo ║   كلمة المرور: admin123                                      ║
echo ║                                                              ║
echo ║ موظف المبيعات:                                               ║
echo ║   اسم المستخدم: sales1                                       ║
echo ║   كلمة المرور: sales123                                      ║
echo ║                                                              ║
echo ║ الميزات الجديدة:                                             ║
echo ║ ✅ رموز QR للعروض والعقود                                    ║
echo ║ ✅ طباعة التقارير المفصلة                                     ║
echo ║ ✅ تصدير Excel محسن                                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 جاري تشغيل البرنامج...
echo.

REM Run the application
dotnet run --configuration Release

echo.
echo 👋 تم إغلاق البرنامج
echo.
echo 💡 نصائح:
echo - استخدم رموز QR لتتبع المشاريع بسهولة
echo - اطبع التقارير المفلترة حسب احتياجاتك
echo - صدر البيانات إلى Excel للتحليل المتقدم
echo.
pause
