using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class DashboardViewModel : BaseViewModel
    {
        private readonly IQuotationService _quotationService;
        private readonly IContractService _contractService;
        private readonly ISalesService _salesService;

        private int _totalProjects = 7;
        private int _completedProjects = 0;
        private int _inProgressProjects = 3;
        private int _thisMonthCompleted = 0;

        public DashboardViewModel(
            IQuotationService quotationService,
            IContractService contractService,
            ISalesService salesService)
        {
            _quotationService = quotationService;
            _contractService = contractService;
            _salesService = salesService;

            RecentActivities = new ObservableCollection<ActivityItem>();
            
            // Initialize commands
            AddNewProjectCommand = new RelayCommand(() => AddNewProject());
            AddMaintenanceRequestCommand = new RelayCommand(() => AddMaintenanceRequest());
            AddNewVisitCommand = new RelayCommand(() => AddNewVisit());
            ApplyFiltersCommand = new RelayCommand(() => ApplyFilters());
            ResetFiltersCommand = new RelayCommand(() => ResetFilters());

            // Load data
            LoadDashboardData();
        }

        public int TotalProjects
        {
            get => _totalProjects;
            set => SetProperty(ref _totalProjects, value);
        }

        public int CompletedProjects
        {
            get => _completedProjects;
            set => SetProperty(ref _completedProjects, value);
        }

        public int InProgressProjects
        {
            get => _inProgressProjects;
            set => SetProperty(ref _inProgressProjects, value);
        }

        public int ThisMonthCompleted
        {
            get => _thisMonthCompleted;
            set => SetProperty(ref _thisMonthCompleted, value);
        }

        public ObservableCollection<ActivityItem> RecentActivities { get; }

        public ICommand AddNewProjectCommand { get; }
        public ICommand AddMaintenanceRequestCommand { get; }
        public ICommand AddNewVisitCommand { get; }
        public ICommand ApplyFiltersCommand { get; }
        public ICommand ResetFiltersCommand { get; }

        private async void LoadDashboardData()
        {
            try
            {
                // Load statistics
                var startOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var contracts = await _contractService.GetAllContractsAsync();
                TotalProjects = contracts.Count();
                CompletedProjects = contracts.Count(c => c.Status == Models.ContractStatus.Completed);
                InProgressProjects = contracts.Count(c => c.Status == Models.ContractStatus.Active);
                ThisMonthCompleted = contracts.Count(c => c.CompletionDate >= startOfMonth && c.CompletionDate <= endOfMonth);

                // Load recent activities (sample data)
                RecentActivities.Clear();
                RecentActivities.Add(new ActivityItem
                {
                    Title = "New quotation created",
                    Description = "Quotation Q20250001 for Khaleel Ahmed",
                    Timestamp = DateTime.Now.AddHours(-2)
                });
                RecentActivities.Add(new ActivityItem
                {
                    Title = "Installation completed",
                    Description = "Project PG-2025/003 installation finished",
                    Timestamp = DateTime.Now.AddHours(-5)
                });
                RecentActivities.Add(new ActivityItem
                {
                    Title = "Manufacturing started",
                    Description = "Job ********** moved to production",
                    Timestamp = DateTime.Now.AddDays(-1)
                });
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }

        private void AddNewProject()
        {
            // Navigate to quotations page or open new project dialog
            System.Diagnostics.Debug.WriteLine("Add New Project clicked");
        }

        private void AddMaintenanceRequest()
        {
            // Open maintenance request dialog
            System.Diagnostics.Debug.WriteLine("Add Maintenance Request clicked");
        }

        private void AddNewVisit()
        {
            // Open new visit dialog
            System.Diagnostics.Debug.WriteLine("Add New Visit clicked");
        }

        private void ApplyFilters()
        {
            // Apply selected filters
            System.Diagnostics.Debug.WriteLine("Apply Filters clicked");
        }

        private void ResetFilters()
        {
            // Reset all filters
            System.Diagnostics.Debug.WriteLine("Reset Filters clicked");
        }
    }

    public class ActivityItem
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
