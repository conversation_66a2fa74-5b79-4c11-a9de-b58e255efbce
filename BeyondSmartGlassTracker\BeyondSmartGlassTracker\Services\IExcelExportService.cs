using BeyondSmartGlassTracker.Models;

namespace BeyondSmartGlassTracker.Services
{
    public interface IExcelExportService
    {
        Task<byte[]> ExportQuotationsToExcelAsync(IEnumerable<Quotation> quotations, string fileName = "Quotations");
        Task<byte[]> ExportContractsToExcelAsync(IEnumerable<Contract> contracts, string fileName = "Contracts");
        Task<byte[]> ExportManufacturingJobsToExcelAsync(IEnumerable<ManufacturingJob> jobs, string fileName = "Manufacturing_Jobs");
        Task<byte[]> ExportInstallationsToExcelAsync(IEnumerable<Installation> installations, string fileName = "Installations");
        Task<byte[]> ExportInventoryToExcelAsync(IEnumerable<InventoryItem> items, string fileName = "Inventory");
        Task<byte[]> ExportClientsToExcelAsync(IEnumerable<Client> clients, string fileName = "Clients");
        Task<byte[]> ExportUsersToExcelAsync(IEnumerable<User> users, string fileName = "Users");
        Task<byte[]> ExportSalesReportToExcelAsync(object salesData, string fileName = "Sales_Report");
        Task<bool> SaveExcelFileAsync(byte[] excelData, string defaultFileName);
    }
}
