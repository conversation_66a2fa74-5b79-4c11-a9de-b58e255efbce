@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - تشخيص المشاكل

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                       تشخيص المشاكل                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري تشخيص المشاكل المحتملة...
echo.

set "issues_found=0"

REM Check .NET installation
echo 1️⃣  فحص تثبيت .NET:
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ .NET مثبت - الإصدار: 
    dotnet --version
) else (
    echo    ❌ .NET غير مثبت أو غير متاح
    echo       الحل: تحميل .NET من https://dotnet.microsoft.com/download/dotnet/
    set /a issues_found+=1
)

echo.

REM Check project files
echo 2️⃣  فحص ملفات المشروع:
if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker.csproj" (
    echo    ✅ ملف المشروع موجود
) else (
    echo    ❌ ملف المشروع مفقود
    echo       الحل: تأكد من وجود مجلد BeyondSmartGlassTracker
    set /a issues_found+=1
)

echo.

REM Check database
echo 3️⃣  فحص قاعدة البيانات:
if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker\BeyondSmartGlass.db" (
    echo    ✅ قاعدة البيانات موجودة في مجلد المشروع
) else if exist "BeyondSmartGlassTracker\BeyondSmartGlass.db" (
    echo    ✅ قاعدة البيانات موجودة في المجلد الرئيسي
) else (
    echo    ⚠️  قاعدة البيانات غير موجودة
    echo       ملاحظة: سيتم إنشاؤها تلقائياً عند أول تشغيل
)

echo.

REM Check permissions
echo 4️⃣  فحص الصلاحيات:
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo    ✅ صلاحيات الكتابة متوفرة
    del test_write.tmp >nul 2>&1
) else (
    echo    ❌ لا توجد صلاحيات كتابة
    echo       الحل: تشغيل البرنامج كمدير
    set /a issues_found+=1
)

echo.

REM Check disk space
echo 5️⃣  فحص المساحة المتاحة:
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set "free_space=%%a"
if defined free_space (
    echo    ✅ المساحة المتاحة: %free_space% بايت
) else (
    echo    ⚠️  لا يمكن تحديد المساحة المتاحة
)

echo.

REM Check running processes
echo 6️⃣  فحص العمليات قيد التشغيل:
tasklist /fi "imagename eq BeyondSmartGlassTracker.exe" 2>nul | find /i "BeyondSmartGlassTracker.exe" >nul
if %errorlevel% equ 0 (
    echo    ⚠️  البرنامج قيد التشغيل حالياً
    echo       ملاحظة: قد يؤثر على بعض العمليات
) else (
    echo    ✅ البرنامج غير قيد التشغيل
)

echo.

REM Check internet connection
echo 7️⃣  فحص الاتصال بالإنترنت:
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ الاتصال بالإنترنت متوفر
) else (
    echo    ❌ لا يوجد اتصال بالإنترنت
    echo       ملاحظة: مطلوب لتحميل المكونات في أول مرة
    set /a issues_found+=1
)

echo.

REM Check antivirus interference
echo 8️⃣  فحص تداخل مكافح الفيروسات:
echo    💡 تأكد من أن مكافح الفيروسات لا يحجب البرنامج
echo       إذا كان البرنامج لا يعمل، جرب تعطيل مكافح الفيروسات مؤقتاً

echo.

REM Try to build project
echo 9️⃣  اختبار بناء المشروع:
cd BeyondSmartGlassTracker >nul 2>&1
if %errorlevel% equ 0 (
    echo    🔨 جاري اختبار البناء...
    dotnet build --verbosity quiet >nul 2>&1
    if %errorlevel% equ 0 (
        echo    ✅ يمكن بناء المشروع بنجاح
    ) else (
        echo    ❌ فشل في بناء المشروع
        echo       الحل: تشغيل UPDATE-PROGRAM.bat أو CLEAN-SYSTEM.bat
        set /a issues_found+=1
    )
    cd .. >nul 2>&1
) else (
    echo    ❌ لا يمكن الوصول لمجلد المشروع
    set /a issues_found+=1
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        نتائج التشخيص                         ║
echo ╚══════════════════════════════════════════════════════════════╝

if %issues_found% equ 0 (
    echo ✅ لم يتم العثور على مشاكل
    echo 🎉 النظام جاهز لتشغيل البرنامج
    echo.
    echo 🚀 لتشغيل البرنامج، استخدم: RUN-PROGRAM.bat
) else (
    echo ❌ تم العثور على %issues_found% مشكلة
    echo.
    echo 🔧 الحلول المقترحة:
    echo    1. تثبيت .NET إذا لم يكن مثبتاً
    echo    2. تشغيل البرنامج كمدير
    echo    3. التأكد من الاتصال بالإنترنت
    echo    4. تشغيل CLEAN-SYSTEM.bat ثم UPDATE-PROGRAM.bat
    echo    5. تعطيل مكافح الفيروسات مؤقتاً
)

echo.
echo 📞 إذا استمرت المشاكل:
echo    - التقط لقطة شاشة من هذه النتائج
echo    - تواصل مع فريق الدعم الفني
echo.
pause
