<UserControl x:Class="BeyondSmartGlassTracker.Controls.QRCodeDisplayControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <materialDesign:Card Style="{StaticResource ModernCard}" 
                        Padding="15" 
                        MaxWidth="300">
        <StackPanel>
            <!-- QR Code Title -->
            <TextBlock Text="{Binding Title, FallbackValue='QR Code'}"
                      Style="{StaticResource SectionHeaderText}"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,10"/>

            <!-- QR Code Image -->
            <Border BorderBrush="{StaticResource LightBlueBrush}"
                   BorderThickness="2"
                   CornerRadius="8"
                   Padding="10"
                   Background="White"
                   HorizontalAlignment="Center">
                <Image x:Name="QRCodeImage"
                      Source="{Binding QRCodeImage}"
                      Width="{Binding QRCodeSize, FallbackValue=150}"
                      Height="{Binding QRCodeSize, FallbackValue=150}"
                      Stretch="Uniform"/>
            </Border>

            <!-- QR Code Info -->
            <TextBlock Text="{Binding Description, FallbackValue='Scan to view details'}"
                      Style="{StaticResource SmallText}"
                      HorizontalAlignment="Center"
                      TextAlignment="Center"
                      Margin="0,10,0,15"/>

            <!-- Action Buttons -->
            <UniformGrid Columns="2" HorizontalAlignment="Stretch">
                <Button Style="{StaticResource SecondaryButton}"
                       Content="Save Image"
                       Margin="0,0,5,0"
                       Command="{Binding SaveQRCodeCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Download" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Save" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button.Content>
                </Button>

                <Button Style="{StaticResource ModernButton}"
                       Content="Print"
                       Margin="5,0,0,0"
                       Command="{Binding PrintQRCodeCommand}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </UniformGrid>

            <!-- QR Code Data (Expandable) -->
            <Expander Header="QR Code Data" 
                     Margin="0,15,0,0"
                     IsExpanded="False">
                <ScrollViewer MaxHeight="100" VerticalScrollBarVisibility="Auto">
                    <TextBlock Text="{Binding QRCodeData}"
                              Style="{StaticResource CaptionText}"
                              TextWrapping="Wrap"
                              FontFamily="Consolas"
                              Background="{StaticResource LightGrayBrush}"
                              Padding="5"/>
                </ScrollViewer>
            </Expander>
        </StackPanel>
    </materialDesign:Card>
</UserControl>
