using BeyondSmartGlassTracker.Models;
using System.Security.Cryptography;
using System.Text;

namespace BeyondSmartGlassTracker.Data
{
    public static class DatabaseSeeder
    {
        public static void SeedData(AppDbContext context)
        {
            // Seed Users
            if (!context.Users.Any())
            {
                var users = new[]
                {
                    new User
                    {
                        Username = "admin",
                        PasswordHash = HashPassword("admin123"),
                        FullName = "System Administrator",
                        Email = "<EMAIL>",
                        Role = UserRole.Admin,
                        IsActive = true
                    },
                    new User
                    {
                        Username = "sales1",
                        PasswordHash = HashPassword("sales123"),
                        FullName = "Sales Representative",
                        Email = "<EMAIL>",
                        Role = UserRole.Sales,
                        IsActive = true
                    }
                };

                context.Users.AddRange(users);
            }

            // Seed Product Types
            if (!context.ProductTypes.Any())
            {
                var productTypes = new[]
                {
                    new ProductType
                    {
                        Name = "Standard Smart Glass",
                        Description = "Standard smart glass with basic opacity control",
                        PricePerSqm = 150.00m,
                        IsActive = true
                    },
                    new ProductType
                    {
                        Name = "Premium Smart Glass",
                        Description = "Premium smart glass with advanced features",
                        PricePerSqm = 250.00m,
                        IsActive = true
                    },
                    new ProductType
                    {
                        Name = "Smart Glass Film",
                        Description = "Retrofit smart glass film for existing windows",
                        PricePerSqm = 100.00m,
                        IsActive = true
                    }
                };

                context.ProductTypes.AddRange(productTypes);
            }

            // Seed Installation Teams
            if (!context.InstallationTeams.Any())
            {
                var teams = new[]
                {
                    new InstallationTeam
                    {
                        Name = "Team Alpha",
                        Description = "Primary installation team",
                        IsActive = true
                    },
                    new InstallationTeam
                    {
                        Name = "Team Beta",
                        Description = "Secondary installation team",
                        IsActive = true
                    }
                };

                context.InstallationTeams.AddRange(teams);
                context.SaveChanges();

                // Seed Team Members
                var teamMembers = new[]
                {
                    new InstallationTeamMember
                    {
                        TeamId = teams[0].Id,
                        Name = "John Smith",
                        Phone = "+1234567890",
                        Role = "Team Lead",
                        IsActive = true
                    },
                    new InstallationTeamMember
                    {
                        TeamId = teams[0].Id,
                        Name = "Mike Johnson",
                        Phone = "+1234567891",
                        Role = "Technician",
                        IsActive = true
                    },
                    new InstallationTeamMember
                    {
                        TeamId = teams[1].Id,
                        Name = "Sarah Wilson",
                        Phone = "+1234567892",
                        Role = "Team Lead",
                        IsActive = true
                    }
                };

                context.InstallationTeamMembers.AddRange(teamMembers);
            }

            // Seed Sample Clients
            if (!context.Clients.Any())
            {
                var clients = new[]
                {
                    new Client
                    {
                        Name = "خليل أحمد",
                        Email = "<EMAIL>",
                        Phone = "+966501234567",
                        City = "الرياض",
                        Address = "حي الملك فهد، شارع الأمير محمد بن عبدالعزيز",
                        Notes = "عميل مهم - مشاريع متعددة"
                    },
                    new Client
                    {
                        Name = "سارة محمد",
                        Email = "<EMAIL>",
                        Phone = "+966502345678",
                        City = "جدة",
                        Address = "حي الروضة، شارع التحلية",
                        Notes = "مشروع فيلا سكنية"
                    },
                    new Client
                    {
                        Name = "أحمد العلي",
                        Email = "<EMAIL>",
                        Phone = "+966503456789",
                        City = "الدمام",
                        Address = "حي الفيصلية، شارع الملك عبدالعزيز",
                        Notes = "مشروع مكتب تجاري"
                    },
                    new Client
                    {
                        Name = "فاطمة الزهراني",
                        Email = "<EMAIL>",
                        Phone = "+966504567890",
                        City = "الرياض",
                        Address = "حي النرجس، شارع الأمير سلطان",
                        Notes = "مشروع شقة سكنية"
                    }
                };

                context.Clients.AddRange(clients);
                context.SaveChanges();
            }

            // Seed Inventory Items
            if (!context.InventoryItems.Any())
            {
                var inventoryItems = new[]
                {
                    new InventoryItem
                    {
                        Name = "Smart Glass Film Roll - Standard",
                        SKU = "SGF-STD-001",
                        Category = InventoryCategory.SmartFilmRolls,
                        Description = "Standard smart glass film roll, 1.5m width",
                        CurrentStock = 50,
                        MinimumStock = 10,
                        MaximumStock = 100,
                        UnitCost = 75.00m,
                        Unit = "sqm",
                        Supplier = "Smart Glass Solutions Ltd",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Smart Glass Film Roll - Premium",
                        SKU = "SGF-PRM-001",
                        Category = InventoryCategory.SmartFilmRolls,
                        Description = "Premium smart glass film roll, 1.5m width",
                        CurrentStock = 25,
                        MinimumStock = 5,
                        MaximumStock = 50,
                        UnitCost = 120.00m,
                        Unit = "sqm",
                        Supplier = "Smart Glass Solutions Ltd",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Control Device - Basic",
                        SKU = "CD-BAS-001",
                        Category = InventoryCategory.Devices,
                        Description = "Basic control device for smart glass",
                        CurrentStock = 25,
                        MinimumStock = 5,
                        MaximumStock = 50,
                        UnitCost = 45.00m,
                        Unit = "pieces",
                        Supplier = "Tech Components Inc",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Control Device - Advanced",
                        SKU = "CD-ADV-001",
                        Category = InventoryCategory.Devices,
                        Description = "Advanced control device with WiFi",
                        CurrentStock = 15,
                        MinimumStock = 3,
                        MaximumStock = 30,
                        UnitCost = 85.00m,
                        Unit = "pieces",
                        Supplier = "Tech Components Inc",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Remote Control - Standard",
                        SKU = "RC-STD-001",
                        Category = InventoryCategory.Remotes,
                        Description = "Standard remote control for smart glass",
                        CurrentStock = 30,
                        MinimumStock = 10,
                        MaximumStock = 60,
                        UnitCost = 25.00m,
                        Unit = "pieces",
                        Supplier = "Remote Tech Ltd",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Remote Control - Smart",
                        SKU = "RC-SMT-001",
                        Category = InventoryCategory.Remotes,
                        Description = "Smart remote with app control",
                        CurrentStock = 20,
                        MinimumStock = 5,
                        MaximumStock = 40,
                        UnitCost = 45.00m,
                        Unit = "pieces",
                        Supplier = "Remote Tech Ltd",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Installation Tools Kit",
                        SKU = "ITK-001",
                        Category = InventoryCategory.Tools,
                        Description = "Complete installation tools kit",
                        CurrentStock = 5,
                        MinimumStock = 2,
                        MaximumStock = 10,
                        UnitCost = 150.00m,
                        Unit = "pieces",
                        Supplier = "Tools & Equipment Co",
                        IsActive = true
                    }
                };

                context.InventoryItems.AddRange(inventoryItems);
            }

            context.SaveChanges();

            // Seed Sample Quotations
            if (!context.Quotations.Any())
            {
                var clients = context.Clients.ToList();
                var users = context.Users.ToList();
                var productTypes = context.ProductTypes.ToList();

                if (clients.Any() && users.Any() && productTypes.Any())
                {
                    var quotations = new[]
                    {
                        new Quotation
                        {
                            QuotationNumber = "Q20250001",
                            ClientId = clients[0].Id,
                            UserId = users[1].Id, // Sales user
                            Status = QuotationStatus.Sent,
                            SubTotal = 15000.00m,
                            DiscountPercentage = 5.00m,
                            DiscountAmount = 750.00m,
                            TotalAmount = 14250.00m,
                            Notes = "مشروع نوافذ المكتب الرئيسي",
                            CreatedAt = DateTime.UtcNow.AddDays(-5),
                            ExpiresAt = DateTime.UtcNow.AddDays(25)
                        },
                        new Quotation
                        {
                            QuotationNumber = "Q20250002",
                            ClientId = clients[1].Id,
                            UserId = users[1].Id,
                            Status = QuotationStatus.Approved,
                            SubTotal = 8500.00m,
                            DiscountPercentage = 0.00m,
                            DiscountAmount = 0.00m,
                            TotalAmount = 8500.00m,
                            Notes = "مشروع غرفة المعيشة",
                            CreatedAt = DateTime.UtcNow.AddDays(-10),
                            ApprovedAt = DateTime.UtcNow.AddDays(-3),
                            ExpiresAt = DateTime.UtcNow.AddDays(20)
                        },
                        new Quotation
                        {
                            QuotationNumber = "Q20250003",
                            ClientId = clients[2].Id,
                            UserId = users[1].Id,
                            Status = QuotationStatus.Draft,
                            SubTotal = 12000.00m,
                            DiscountPercentage = 10.00m,
                            DiscountAmount = 1200.00m,
                            TotalAmount = 10800.00m,
                            Notes = "مشروع مكتب تجاري - قيد المراجعة",
                            CreatedAt = DateTime.UtcNow.AddDays(-2),
                            ExpiresAt = DateTime.UtcNow.AddDays(28)
                        }
                    };

                    context.Quotations.AddRange(quotations);
                    context.SaveChanges();

                    // Add quotation items
                    var quotationItems = new[]
                    {
                        // Items for first quotation
                        new QuotationItem
                        {
                            QuotationId = quotations[0].Id,
                            ProductTypeId = productTypes[0].Id,
                            AreaSqm = 50.00m,
                            DeviceCount = 10,
                            PricePerSqm = 150.00m,
                            TotalPrice = 7500.00m,
                            Description = "نوافذ المكاتب الإدارية"
                        },
                        new QuotationItem
                        {
                            QuotationId = quotations[0].Id,
                            ProductTypeId = productTypes[1].Id,
                            AreaSqm = 30.00m,
                            DeviceCount = 6,
                            PricePerSqm = 250.00m,
                            TotalPrice = 7500.00m,
                            Description = "نوافذ غرفة الاجتماعات"
                        },
                        // Items for second quotation
                        new QuotationItem
                        {
                            QuotationId = quotations[1].Id,
                            ProductTypeId = productTypes[2].Id,
                            AreaSqm = 85.00m,
                            DeviceCount = 17,
                            PricePerSqm = 100.00m,
                            TotalPrice = 8500.00m,
                            Description = "فيلم ذكي لنوافذ غرفة المعيشة"
                        },
                        // Items for third quotation
                        new QuotationItem
                        {
                            QuotationId = quotations[2].Id,
                            ProductTypeId = productTypes[0].Id,
                            AreaSqm = 80.00m,
                            DeviceCount = 16,
                            PricePerSqm = 150.00m,
                            TotalPrice = 12000.00m,
                            Description = "زجاج ذكي للمكتب التجاري"
                        }
                    };

                    context.QuotationItems.AddRange(quotationItems);
                    context.SaveChanges();
                }
            }
        }

        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
