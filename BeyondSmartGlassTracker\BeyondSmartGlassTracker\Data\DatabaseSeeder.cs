using BeyondSmartGlassTracker.Models;
using System.Security.Cryptography;
using System.Text;

namespace BeyondSmartGlassTracker.Data
{
    public static class DatabaseSeeder
    {
        public static void SeedData(AppDbContext context)
        {
            // Seed Users
            if (!context.Users.Any())
            {
                var users = new[]
                {
                    new User
                    {
                        Username = "admin",
                        PasswordHash = HashPassword("admin123"),
                        FullName = "System Administrator",
                        Email = "<EMAIL>",
                        Role = UserRole.Admin,
                        IsActive = true
                    },
                    new User
                    {
                        Username = "sales1",
                        PasswordHash = HashPassword("sales123"),
                        FullName = "Sales Representative",
                        Email = "<EMAIL>",
                        Role = UserRole.Sales,
                        IsActive = true
                    }
                };

                context.Users.AddRange(users);
            }

            // Seed Product Types
            if (!context.ProductTypes.Any())
            {
                var productTypes = new[]
                {
                    new ProductType
                    {
                        Name = "Standard Smart Glass",
                        Description = "Standard smart glass with basic opacity control",
                        PricePerSqm = 150.00m,
                        IsActive = true
                    },
                    new ProductType
                    {
                        Name = "Premium Smart Glass",
                        Description = "Premium smart glass with advanced features",
                        PricePerSqm = 250.00m,
                        IsActive = true
                    },
                    new ProductType
                    {
                        Name = "Smart Glass Film",
                        Description = "Retrofit smart glass film for existing windows",
                        PricePerSqm = 100.00m,
                        IsActive = true
                    }
                };

                context.ProductTypes.AddRange(productTypes);
            }

            // Seed Installation Teams
            if (!context.InstallationTeams.Any())
            {
                var teams = new[]
                {
                    new InstallationTeam
                    {
                        Name = "Team Alpha",
                        Description = "Primary installation team",
                        IsActive = true
                    },
                    new InstallationTeam
                    {
                        Name = "Team Beta",
                        Description = "Secondary installation team",
                        IsActive = true
                    }
                };

                context.InstallationTeams.AddRange(teams);
                context.SaveChanges();

                // Seed Team Members
                var teamMembers = new[]
                {
                    new InstallationTeamMember
                    {
                        TeamId = teams[0].Id,
                        Name = "John Smith",
                        Phone = "+1234567890",
                        Role = "Team Lead",
                        IsActive = true
                    },
                    new InstallationTeamMember
                    {
                        TeamId = teams[0].Id,
                        Name = "Mike Johnson",
                        Phone = "+1234567891",
                        Role = "Technician",
                        IsActive = true
                    },
                    new InstallationTeamMember
                    {
                        TeamId = teams[1].Id,
                        Name = "Sarah Wilson",
                        Phone = "+1234567892",
                        Role = "Team Lead",
                        IsActive = true
                    }
                };

                context.InstallationTeamMembers.AddRange(teamMembers);
            }

            // Seed Inventory Items
            if (!context.InventoryItems.Any())
            {
                var inventoryItems = new[]
                {
                    new InventoryItem
                    {
                        Name = "Smart Glass Film Roll - Standard",
                        SKU = "SGF-STD-001",
                        Category = InventoryCategory.SmartFilmRolls,
                        Description = "Standard smart glass film roll, 1.5m width",
                        CurrentStock = 50,
                        MinimumStock = 10,
                        MaximumStock = 100,
                        UnitCost = 75.00m,
                        Unit = "sqm",
                        Supplier = "Smart Glass Solutions Ltd",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Control Device - Basic",
                        SKU = "CD-BAS-001",
                        Category = InventoryCategory.Devices,
                        Description = "Basic control device for smart glass",
                        CurrentStock = 25,
                        MinimumStock = 5,
                        MaximumStock = 50,
                        UnitCost = 45.00m,
                        Unit = "pieces",
                        Supplier = "Tech Components Inc",
                        IsActive = true
                    },
                    new InventoryItem
                    {
                        Name = "Remote Control - Standard",
                        SKU = "RC-STD-001",
                        Category = InventoryCategory.Remotes,
                        Description = "Standard remote control for smart glass",
                        CurrentStock = 30,
                        MinimumStock = 10,
                        MaximumStock = 60,
                        UnitCost = 25.00m,
                        Unit = "pieces",
                        Supplier = "Remote Tech Ltd",
                        IsActive = true
                    }
                };

                context.InventoryItems.AddRange(inventoryItems);
            }

            context.SaveChanges();
        }

        private static string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
                return Convert.ToBase64String(hashedBytes);
            }
        }
    }
}
