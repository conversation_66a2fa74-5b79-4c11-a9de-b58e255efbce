<Window x:Class="BeyondSmartGlassTracker.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Beyond Smart Glass Tracker" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{StaticResource LightGrayBrush}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0" 
                Background="{StaticResource PrimaryBlueBrush}"
                BorderThickness="0,0,1,0"
                BorderBrush="{StaticResource LightBlueBrush}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Logo/Header -->
                <StackPanel Grid.Row="0" Margin="20" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Glasses" 
                                           Foreground="White" 
                                           Width="40" 
                                           Height="40"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="Beyond Smart Glass"
                              Foreground="White"
                              FontFamily="{StaticResource PrimaryFont}"
                              FontSize="16"
                              FontWeight="Bold"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                    <TextBlock Text="Tracker"
                              Foreground="White"
                              FontFamily="{StaticResource SecondaryFont}"
                              FontSize="12"
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- Navigation Menu -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,20,0,0">
                        
                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Dashboard">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Dashboard" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Quotations -->
                        <Button x:Name="QuotationsButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Quotations">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="FileDocument" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Quotations" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Contracts -->
                        <Button x:Name="ContractsButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Contracts">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="FileDocumentEdit" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Contracts" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Manufacturing -->
                        <Button x:Name="ManufacturingButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Manufacturing">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="Factory" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Manufacturing" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Installation -->
                        <Button x:Name="InstallationButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Installation">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="CalendarClock" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Installation" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Inventory -->
                        <Button x:Name="InventoryButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Inventory">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="Package" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Inventory" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Sales Dashboard -->
                        <Button x:Name="SalesButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Sales">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Sales Dashboard" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                        <!-- Admin Panel -->
                        <Button x:Name="AdminButton"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Height="50"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Foreground="White"
                               Margin="0,2"
                               Command="{Binding NavigateCommand}"
                               CommandParameter="Admin"
                               Visibility="{Binding IsAdmin, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal" Margin="20,0">
                                <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Admin Panel" Margin="15,0,0,0" VerticalAlignment="Center" FontSize="14"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>

                <!-- User Info & Logout -->
                <StackPanel Grid.Row="2" Margin="20">
                    <Separator Foreground="White" Opacity="0.3" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding CurrentUser.FullName}"
                              Foreground="White"
                              FontFamily="{StaticResource PrimaryFont}"
                              FontSize="12"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding CurrentUser.Role}"
                              Foreground="White"
                              FontFamily="{StaticResource SecondaryFont}"
                              FontSize="10"
                              HorizontalAlignment="Center"
                              Opacity="0.8"/>
                    <Button Content="Logout"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Foreground="White"
                           BorderBrush="White"
                           Height="35"
                           Margin="0,10,0,0"
                           Command="{Binding LogoutCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <Border Grid.Row="0" 
                    Background="{StaticResource WhiteBrush}"
                    BorderThickness="0,0,0,1"
                    BorderBrush="{StaticResource LightBlueBrush}"
                    Height="60">
                <Grid Margin="20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Page Title -->
                    <TextBlock Grid.Column="0"
                              Text="{Binding CurrentPageTitle}"
                              FontFamily="{StaticResource PrimaryFont}"
                              FontSize="20"
                              FontWeight="Bold"
                              Foreground="{StaticResource PrimaryBlueBrush}"
                              VerticalAlignment="Center"/>

                    <!-- Quick Actions -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="Refresh">
                            <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="Settings">
                            <materialDesign:PackIcon Kind="Cog" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Content Frame -->
            <Frame Grid.Row="1" 
                   x:Name="MainFrame"
                   NavigationUIVisibility="Hidden"
                   Background="{StaticResource LightGrayBrush}"
                   Content="{Binding CurrentView}"/>
        </Grid>
    </Grid>
</Window>
