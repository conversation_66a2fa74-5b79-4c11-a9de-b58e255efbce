{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/BeyondSmartGlassTracker.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "build-release", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/BeyondSmartGlassTracker.csproj", "--configuration", "Release", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/BeyondSmartGlassTracker.csproj", "--configuration", "Release", "--runtime", "win-x64", "--self-contained", "true", "-p:PublishSingleFile=true", "-p:PublishReadyToRun=true", "--output", "${workspaceFolder}/BeyondSmartGlassTracker/Release"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/BeyondSmartGlassTracker.csproj"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/BeyondSmartGlassTracker.csproj"], "problemMatcher": "$msCompile"}]}