using System.Windows;
using BeyondSmartGlassTracker.Data;
using BeyondSmartGlassTracker.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace BeyondSmartGlassTracker
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Database
                    services.AddDbContext<AppDbContext>(options =>
                        options.UseSqlite("Data Source=BeyondSmartGlass.db"));

                    // Services
                    services.AddSingleton<IAuthenticationService, AuthenticationService>();
                    services.AddSingleton<IQuotationService, QuotationService>();
                    services.AddSingleton<IContractService, ContractService>();
                    services.AddSingleton<IManufacturingService, ManufacturingService>();
                    services.AddSingleton<IInstallationService, InstallationService>();
                    services.AddSingleton<IInventoryService, InventoryService>();
                    services.AddSingleton<ISalesService, SalesService>();
                    services.AddSingleton<IPdfExportService, PdfExportService>();
                    services.AddSingleton<IExcelExportService, ExcelExportService>();

                    // ViewModels
                    services.AddTransient<ViewModels.LoginViewModel>();
                    services.AddTransient<ViewModels.MainViewModel>();
                    services.AddTransient<ViewModels.DashboardViewModel>();
                    services.AddTransient<ViewModels.QuotationsViewModel>();
                    services.AddTransient<ViewModels.ContractsViewModel>();
                    services.AddTransient<ViewModels.ManufacturingViewModel>();
                    services.AddTransient<ViewModels.InstallationViewModel>();
                    services.AddTransient<ViewModels.InventoryViewModel>();
                    services.AddTransient<ViewModels.SalesViewModel>();
                    services.AddTransient<ViewModels.AdminViewModel>();
                })
                .Build();

            // Initialize database
            using (var scope = _host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                context.Database.EnsureCreated();
                
                // Seed initial data
                DatabaseSeeder.SeedData(context);
            }

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            base.OnExit(e);
        }

        public static T GetService<T>() where T : class
        {
            return ((App)Current)._host!.Services.GetRequiredService<T>();
        }
    }
}
