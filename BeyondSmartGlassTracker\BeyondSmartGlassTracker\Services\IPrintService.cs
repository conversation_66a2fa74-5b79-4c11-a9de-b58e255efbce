using BeyondSmartGlassTracker.Models;

namespace BeyondSmartGlassTracker.Services
{
    public interface IPrintService
    {
        Task<bool> PrintQuotationsReportAsync(IEnumerable<Quotation> quotations, string title = "Quotations Report");
        Task<bool> PrintContractsReportAsync(IEnumerable<Contract> contracts, string title = "Contracts Report");
        Task<bool> PrintManufacturingReportAsync(IEnumerable<ManufacturingJob> jobs, string title = "Manufacturing Report");
        Task<bool> PrintInventoryReportAsync(IEnumerable<InventoryItem> items, string title = "Inventory Report");
        Task<bool> PrintSalesReportAsync(object salesData, string title = "Sales Report");
        Task<bool> PrintQuotationWithQRAsync(Quotation quotation);
        Task<bool> PrintContractWithQRAsync(Contract contract);
        Task<bool> PrintFilteredListWithQRAsync<T>(IEnumerable<T> items, string title, Func<T, string> qrTextGenerator);
    }
}
