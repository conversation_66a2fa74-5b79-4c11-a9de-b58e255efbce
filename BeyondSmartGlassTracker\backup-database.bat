@echo off
chcp 65001 >nul
title نسخ احتياطي لقاعدة البيانات

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نسخ احتياطي لقاعدة البيانات                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "DB_FILE=BeyondSmartGlassTracker\BeyondSmartGlass.db"
set "BACKUP_DIR=Backups"
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"

REM Create backup directory if it doesn't exist
if not exist "%BACKUP_DIR%" (
    mkdir "%BACKUP_DIR%"
    echo ✅ تم إنشاء مجلد النسخ الاحتياطية
)

REM Check if database exists
if not exist "%DB_FILE%" (
    echo ❌ لم يتم العثور على قاعدة البيانات
    echo يرجى تشغيل البرنامج أولاً لإنشاء قاعدة البيانات
    pause
    exit /b 1
)

REM Create backup
set "BACKUP_FILE=%BACKUP_DIR%\BeyondSmartGlass_Backup_%TIMESTAMP%.db"
copy "%DB_FILE%" "%BACKUP_FILE%" >nul

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء النسخة الاحتياطية بنجاح
    echo 📁 الملف: %BACKUP_FILE%
    echo 📊 حجم الملف:
    for %%A in ("%BACKUP_FILE%") do echo    %%~zA bytes
    echo.
    echo 💡 نصيحة: احتفظ بالنسخ الاحتياطية في مكان آمن
) else (
    echo ❌ فشل في إنشاء النسخة الاحتياطية
)

echo.
pause
