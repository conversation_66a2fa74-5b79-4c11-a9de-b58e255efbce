@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - معلومات النظام

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                      معلومات النظام                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🖥️  معلومات نظام التشغيل:
echo ═══════════════════════════════════════
systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
echo.

echo 💾 معلومات الذاكرة:
echo ═══════════════════════════════════════
systeminfo | findstr /B /C:"Total Physical Memory" /C:"Available Physical Memory"
echo.

echo 💿 معلومات القرص الصلب:
echo ═══════════════════════════════════════
wmic logicaldisk get size,freespace,caption
echo.

echo 🔧 معلومات .NET:
echo ═══════════════════════════════════════
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET مثبت
    echo الإصدار: 
    dotnet --version
    echo.
    echo تفاصيل .NET:
    dotnet --info | findstr /B /C:"Version:" /C:"Runtime Environment:" /C:"Host:"
) else (
    echo ❌ .NET غير مثبت
)
echo.

echo 📁 معلومات المشروع:
echo ═══════════════════════════════════════
if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker.csproj" (
    echo ✅ ملفات المشروع موجودة
    echo المسار: %cd%\BeyondSmartGlassTracker\
    
    REM Get project file size
    for %%A in ("BeyondSmartGlassTracker\BeyondSmartGlassTracker.csproj") do echo حجم ملف المشروع: %%~zA بايت
    
    REM Count source files
    for /f %%A in ('dir "BeyondSmartGlassTracker\*.cs" /s /b ^| find /c /v ""') do echo عدد ملفات C#: %%A
    for /f %%A in ('dir "BeyondSmartGlassTracker\*.xaml" /s /b ^| find /c /v ""') do echo عدد ملفات XAML: %%A
    
) else (
    echo ❌ ملفات المشروع مفقودة
)
echo.

echo 🗄️  معلومات قاعدة البيانات:
echo ═══════════════════════════════════════
if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker\BeyondSmartGlass.db" (
    set "dbpath=BeyondSmartGlassTracker\BeyondSmartGlassTracker\BeyondSmartGlass.db"
    echo ✅ قاعدة البيانات موجودة في مجلد المشروع
) else if exist "BeyondSmartGlassTracker\BeyondSmartGlass.db" (
    set "dbpath=BeyondSmartGlassTracker\BeyondSmartGlass.db"
    echo ✅ قاعدة البيانات موجودة في المجلد الرئيسي
) else (
    echo ❌ قاعدة البيانات غير موجودة
    set "dbpath="
)

if defined dbpath (
    for %%A in ("%dbpath%") do (
        echo حجم قاعدة البيانات: %%~zA بايت
        echo تاريخ آخر تعديل: %%~tA
    )
)
echo.

echo 📦 معلومات النسخ الاحتياطية:
echo ═══════════════════════════════════════
if exist "Backups" (
    for /f %%A in ('dir "Backups\*.db" /b 2^>nul ^| find /c /v ""') do (
        if %%A gtr 0 (
            echo ✅ عدد النسخ الاحتياطية: %%A
            echo آخر نسخة احتياطية:
            for /f "tokens=*" %%f in ('dir "Backups\*.db" /b /o:d 2^>nul ^| tail -1') do echo    %%f
        ) else (
            echo ❌ لا توجد نسخ احتياطية
        )
    )
) else (
    echo ❌ مجلد النسخ الاحتياطية غير موجود
)
echo.

echo 🌐 معلومات الشبكة:
echo ═══════════════════════════════════════
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الاتصال بالإنترنت متوفر
) else (
    echo ❌ لا يوجد اتصال بالإنترنت
)

ipconfig | findstr /B /C:"IPv4 Address"
echo.

echo 🔒 معلومات الأمان:
echo ═══════════════════════════════════════
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ يتم التشغيل بصلاحيات المدير
) else (
    echo ⚠️  يتم التشغيل بصلاحيات المستخدم العادي
)

REM Check Windows Defender status
sc query windefend >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Windows Defender نشط
) else (
    echo ⚠️  Windows Defender غير نشط أو غير متوفر
)
echo.

echo 📊 إحصائيات الاستخدام:
echo ═══════════════════════════════════════
echo تاريخ إنشاء هذا التقرير: %date% %time%
echo مسار المجلد الحالي: %cd%
echo اسم المستخدم: %username%
echo اسم الكمبيوتر: %computername%
echo.

echo 💡 ملاحظات:
echo ═══════════════════════════════════════
echo - هذا التقرير يساعد في تشخيص المشاكل
echo - احتفظ بهذه المعلومات عند طلب الدعم الفني
echo - تأكد من توفر المتطلبات الأساسية للبرنامج
echo.

pause
