using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class QuotationsViewModel : BaseViewModel
    {
        private readonly IQuotationService _quotationService;
        private readonly IExcelExportService _excelExportService;
        private readonly IQRCodeService _qrCodeService;
        private readonly IPrintService _printService;

        public QuotationsViewModel(
            IQuotationService quotationService,
            IExcelExportService excelExportService,
            IQRCodeService qrCodeService,
            IPrintService printService)
        {
            _quotationService = quotationService;
            _excelExportService = excelExportService;
            _qrCodeService = qrCodeService;
            _printService = printService;
            Quotations = new ObservableCollection<Quotation>();

            NewQuotationCommand = new RelayCommand(() => NewQuotation());
            ViewQuotationCommand = new RelayCommand<Quotation>(quotation => ViewQuotation(quotation));
            EditQuotationCommand = new RelayCommand<Quotation>(quotation => EditQuotation(quotation));
            ExportPdfCommand = new RelayCommand<Quotation>(quotation => ExportPdf(quotation));
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());
            ShowQRCodeCommand = new RelayCommand<Quotation>(quotation => ShowQRCode(quotation));
            PrintReportCommand = new RelayCommand(async () => await PrintReport());
            PrintWithQRCommand = new RelayCommand(async () => await PrintWithQR());

            LoadQuotations();
        }

        public ObservableCollection<Quotation> Quotations { get; }

        public ICommand NewQuotationCommand { get; }
        public ICommand ViewQuotationCommand { get; }
        public ICommand EditQuotationCommand { get; }
        public ICommand ExportPdfCommand { get; }
        public ICommand ExportToExcelCommand { get; }
        public ICommand ShowQRCodeCommand { get; }
        public ICommand PrintReportCommand { get; }
        public ICommand PrintWithQRCommand { get; }

        private async void LoadQuotations()
        {
            try
            {
                var quotations = await _quotationService.GetAllQuotationsAsync();
                Quotations.Clear();
                foreach (var quotation in quotations)
                {
                    Quotations.Add(quotation);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading quotations: {ex.Message}");
            }
        }

        private void NewQuotation()
        {
            System.Diagnostics.Debug.WriteLine("New Quotation clicked");
        }

        private void ViewQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"View Quotation: {quotation.QuotationNumber}");
        }

        private void EditQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"Edit Quotation: {quotation.QuotationNumber}");
        }

        private async void ExportPdf(Quotation? quotation)
        {
            if (quotation != null)
            {
                try
                {
                    var pdfBytes = await _quotationService.ExportToPdfAsync(quotation.Id);
                    // Save file dialog and save PDF
                    System.Diagnostics.Debug.WriteLine($"Export PDF for: {quotation.QuotationNumber}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error exporting PDF: {ex.Message}");
                }
            }
        }

        private async Task ExportToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportQuotationsToExcelAsync(Quotations);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Quotations");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Quotations exported to Excel successfully");
                    // You could show a success message to the user here
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                    // You could show an error message to the user here
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
                // You could show an error message to the user here
            }
        }

        private void ShowQRCode(Quotation? quotation)
        {
            if (quotation != null)
            {
                try
                {
                    var qrWindow = new Views.QRCodeWindow();
                    var qrViewModel = App.GetService<QRCodeDisplayViewModel>();

                    var qrText = _qrCodeService.GenerateQuotationQRText(quotation);
                    qrViewModel.GenerateQRCode(qrText, $"Quotation {quotation.QuotationNumber}",
                        $"Client: {quotation.Client?.Name} | Amount: ${quotation.TotalAmount:F2}");

                    qrWindow.DataContext = qrViewModel;
                    qrWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error showing QR code: {ex.Message}");
                }
            }
        }

        private async Task PrintReport()
        {
            try
            {
                var success = await _printService.PrintQuotationsReportAsync(Quotations, "Quotations Report");
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Quotations report printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing report: {ex.Message}");
            }
        }

        private async Task PrintWithQR()
        {
            try
            {
                var quotationsWithQR = Quotations.Where(q => q.Status == QuotationStatus.Sent || q.Status == QuotationStatus.Approved);
                var success = await _printService.PrintFilteredListWithQRAsync(
                    quotationsWithQR,
                    "Quotations with QR Codes",
                    q => _qrCodeService.GenerateQuotationQRText(q));

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Quotations with QR codes printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print with QR was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing with QR codes: {ex.Message}");
            }
        }
    }
}
