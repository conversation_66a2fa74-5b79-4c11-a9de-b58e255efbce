using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class QuotationsViewModel : BaseViewModel
    {
        private readonly IQuotationService _quotationService;
        private readonly IExcelExportService _excelExportService;

        public QuotationsViewModel(IQuotationService quotationService, IExcelExportService excelExportService)
        {
            _quotationService = quotationService;
            _excelExportService = excelExportService;
            Quotations = new ObservableCollection<Quotation>();

            NewQuotationCommand = new RelayCommand(() => NewQuotation());
            ViewQuotationCommand = new RelayCommand<Quotation>(quotation => ViewQuotation(quotation));
            EditQuotationCommand = new RelayCommand<Quotation>(quotation => EditQuotation(quotation));
            ExportPdfCommand = new RelayCommand<Quotation>(quotation => ExportPdf(quotation));
            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());

            LoadQuotations();
        }

        public ObservableCollection<Quotation> Quotations { get; }

        public ICommand NewQuotationCommand { get; }
        public ICommand ViewQuotationCommand { get; }
        public ICommand EditQuotationCommand { get; }
        public ICommand ExportPdfCommand { get; }
        public ICommand ExportToExcelCommand { get; }

        private async void LoadQuotations()
        {
            try
            {
                var quotations = await _quotationService.GetAllQuotationsAsync();
                Quotations.Clear();
                foreach (var quotation in quotations)
                {
                    Quotations.Add(quotation);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading quotations: {ex.Message}");
            }
        }

        private void NewQuotation()
        {
            System.Diagnostics.Debug.WriteLine("New Quotation clicked");
        }

        private void ViewQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"View Quotation: {quotation.QuotationNumber}");
        }

        private void EditQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"Edit Quotation: {quotation.QuotationNumber}");
        }

        private async void ExportPdf(Quotation? quotation)
        {
            if (quotation != null)
            {
                try
                {
                    var pdfBytes = await _quotationService.ExportToPdfAsync(quotation.Id);
                    // Save file dialog and save PDF
                    System.Diagnostics.Debug.WriteLine($"Export PDF for: {quotation.QuotationNumber}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error exporting PDF: {ex.Message}");
                }
            }
        }

        private async Task ExportToExcel()
        {
            try
            {
                var excelData = await _excelExportService.ExportQuotationsToExcelAsync(Quotations);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Quotations");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Quotations exported to Excel successfully");
                    // You could show a success message to the user here
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                    // You could show an error message to the user here
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
                // You could show an error message to the user here
            }
        }
    }
}
