using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class QuotationsViewModel : BaseViewModel
    {
        private readonly IQuotationService _quotationService;

        public QuotationsViewModel(IQuotationService quotationService)
        {
            _quotationService = quotationService;
            Quotations = new ObservableCollection<Quotation>();
            
            NewQuotationCommand = new RelayCommand(() => NewQuotation());
            ViewQuotationCommand = new RelayCommand<Quotation>(quotation => ViewQuotation(quotation));
            EditQuotationCommand = new RelayCommand<Quotation>(quotation => EditQuotation(quotation));
            ExportPdfCommand = new RelayCommand<Quotation>(quotation => ExportPdf(quotation));

            LoadQuotations();
        }

        public ObservableCollection<Quotation> Quotations { get; }

        public ICommand NewQuotationCommand { get; }
        public ICommand ViewQuotationCommand { get; }
        public ICommand EditQuotationCommand { get; }
        public ICommand ExportPdfCommand { get; }

        private async void LoadQuotations()
        {
            try
            {
                var quotations = await _quotationService.GetAllQuotationsAsync();
                Quotations.Clear();
                foreach (var quotation in quotations)
                {
                    Quotations.Add(quotation);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading quotations: {ex.Message}");
            }
        }

        private void NewQuotation()
        {
            System.Diagnostics.Debug.WriteLine("New Quotation clicked");
        }

        private void ViewQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"View Quotation: {quotation.QuotationNumber}");
        }

        private void EditQuotation(Quotation? quotation)
        {
            if (quotation != null)
                System.Diagnostics.Debug.WriteLine($"Edit Quotation: {quotation.QuotationNumber}");
        }

        private async void ExportPdf(Quotation? quotation)
        {
            if (quotation != null)
            {
                try
                {
                    var pdfBytes = await _quotationService.ExportToPdfAsync(quotation.Id);
                    // Save file dialog and save PDF
                    System.Diagnostics.Debug.WriteLine($"Export PDF for: {quotation.QuotationNumber}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error exporting PDF: {ex.Message}");
                }
            }
        }
    }
}
