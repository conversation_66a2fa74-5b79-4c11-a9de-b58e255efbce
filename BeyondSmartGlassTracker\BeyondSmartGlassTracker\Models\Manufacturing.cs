using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlassTracker.Models
{
    public class ManufacturingJob
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string JobNumber { get; set; } = string.Empty;

        public int ContractId { get; set; }
        public virtual Contract Contract { get; set; } = null!;

        public ManufacturingStatus Status { get; set; } = ManufacturingStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public DateTime? EstimatedCompletionDate { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public int Priority { get; set; } = 1; // 1 = Low, 2 = Medium, 3 = High

        // Navigation properties
        public virtual ICollection<ManufacturingItem> Items { get; set; } = new List<ManufacturingItem>();
    }

    public class ManufacturingItem
    {
        public int Id { get; set; }

        public int ManufacturingJobId { get; set; }
        public virtual ManufacturingJob ManufacturingJob { get; set; } = null!;

        public int ProductTypeId { get; set; }
        public virtual ProductType ProductType { get; set; } = null!;

        public decimal AreaSqm { get; set; }

        public int DeviceCount { get; set; }

        public int CompletedDevices { get; set; } = 0;

        public ManufacturingItemStatus Status { get; set; } = ManufacturingItemStatus.Pending;

        [StringLength(500)]
        public string? Notes { get; set; }
    }

    public enum ManufacturingStatus
    {
        Pending = 1,
        InProduction = 2,
        Finished = 3,
        OnHold = 4,
        Cancelled = 5
    }

    public enum ManufacturingItemStatus
    {
        Pending = 1,
        InProgress = 2,
        Completed = 3,
        OnHold = 4
    }
}
