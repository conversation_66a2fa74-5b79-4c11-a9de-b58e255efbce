<UserControl x:Class="BeyondSmartGlassTracker.Views.InventoryView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Inventory Manager"
                      Style="{StaticResource HeaderText}"/>
            <TextBlock Text="Manage smart glass components, materials, and stock levels with comprehensive reporting."
                      Style="{StaticResource BodyText}"/>
        </StackPanel>

        <!-- Action Bar -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBox materialDesign:HintAssist.Hint="Search inventory items..."
                            Width="300"
                            Margin="0,0,15,0"/>
                    <ComboBox materialDesign:HintAssist.Hint="Category"
                             Width="150"
                             Margin="0,0,15,0">
                        <ComboBoxItem Content="All Categories"/>
                        <ComboBoxItem Content="Smart Film Rolls"/>
                        <ComboBoxItem Content="Devices"/>
                        <ComboBoxItem Content="Remotes"/>
                        <ComboBoxItem Content="Accessories"/>
                        <ComboBoxItem Content="Tools"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource SecondaryButton}"
                           Margin="0,0,5,0"
                           Command="{Binding ExportToExcelCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExcel" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Export Excel" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource WarningButton}"
                           Margin="5,0,10,0"
                           Command="{Binding PrintReportCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="Print Report" Margin="5,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource ModernButton}"
                           Command="{Binding NewItemCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" VerticalAlignment="Center"/>
                            <TextBlock Text="New Item" Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Inventory Items List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource ModernCard}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="Package" Width="64" Height="64" Foreground="{StaticResource PrimaryBlueBrush}"/>
                <TextBlock Text="Inventory Items" Style="{StaticResource HeaderText}" HorizontalAlignment="Center" Margin="0,20,0,10"/>
                <TextBlock Text="Inventory management functionality will be fully implemented here." Style="{StaticResource BodyText}" HorizontalAlignment="Center"/>
                <TextBlock Text="Print and Excel export functionality is ready to use." Style="{StaticResource SmallText}" HorizontalAlignment="Center" Margin="0,10,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
