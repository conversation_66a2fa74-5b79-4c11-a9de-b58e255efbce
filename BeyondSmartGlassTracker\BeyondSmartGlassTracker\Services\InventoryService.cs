using BeyondSmartGlassTracker.Data;
using BeyondSmartGlassTracker.Models;
using Microsoft.EntityFrameworkCore;

namespace BeyondSmartGlassTracker.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly AppDbContext _context;

        public InventoryService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<InventoryItem>> GetAllItemsAsync()
        {
            return await _context.InventoryItems
                .Where(i => i.IsActive)
                .OrderBy(i => i.Name)
                .ToListAsync();
        }

        public async Task<InventoryItem?> GetItemByIdAsync(int id)
        {
            return await _context.InventoryItems
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<InventoryItem> CreateItemAsync(InventoryItem item)
        {
            item.CreatedAt = DateTime.UtcNow;
            _context.InventoryItems.Add(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<InventoryItem> UpdateItemAsync(InventoryItem item)
        {
            item.LastUpdated = DateTime.UtcNow;
            _context.InventoryItems.Update(item);
            await _context.SaveChangesAsync();
            return item;
        }

        public async Task<bool> DeleteItemAsync(int id)
        {
            var item = await _context.InventoryItems.FindAsync(id);
            if (item == null) return false;

            item.IsActive = false;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsAsync(int itemId)
        {
            return await _context.InventoryTransactions
                .Include(t => t.User)
                .Where(t => t.InventoryItemId == itemId)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync();
        }

        public async Task<InventoryTransaction> AddTransactionAsync(InventoryTransaction transaction)
        {
            transaction.CreatedAt = DateTime.UtcNow;
            transaction.TotalCost = transaction.Quantity * transaction.UnitCost;

            _context.InventoryTransactions.Add(transaction);

            // Update inventory item stock
            var item = await _context.InventoryItems.FindAsync(transaction.InventoryItemId);
            if (item != null)
            {
                switch (transaction.Type)
                {
                    case TransactionType.Purchase:
                    case TransactionType.Return:
                        item.CurrentStock += transaction.Quantity;
                        break;
                    case TransactionType.Sale:
                    case TransactionType.Damage:
                        item.CurrentStock -= transaction.Quantity;
                        break;
                    case TransactionType.Adjustment:
                        item.CurrentStock = transaction.Quantity;
                        break;
                }
                item.LastUpdated = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return transaction;
        }
    }

    public class SalesService : ISalesService
    {
        private readonly AppDbContext _context;

        public SalesService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Contracts
                .Where(c => c.SignedAt >= startDate && c.SignedAt <= endDate)
                .SumAsync(c => c.TotalAmount);
        }

        public async Task<int> GetTotalQuotationsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Quotations
                .Where(q => q.CreatedAt >= startDate && q.CreatedAt <= endDate)
                .CountAsync();
        }

        public async Task<int> GetTotalContractsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Contracts
                .Where(c => c.SignedAt >= startDate && c.SignedAt <= endDate)
                .CountAsync();
        }

        public async Task<IEnumerable<object>> GetSalesChartDataAsync(DateTime startDate, DateTime endDate)
        {
            var data = await _context.Contracts
                .Where(c => c.SignedAt >= startDate && c.SignedAt <= endDate)
                .GroupBy(c => new { Year = c.SignedAt.Year, Month = c.SignedAt.Month })
                .Select(g => new
                {
                    Period = $"{g.Key.Year}-{g.Key.Month:D2}",
                    TotalSales = g.Sum(c => c.TotalAmount),
                    ContractCount = g.Count()
                })
                .OrderBy(x => x.Period)
                .ToListAsync();

            return data.Cast<object>();
        }

        public async Task<IEnumerable<object>> GetTopClientsAsync(int count = 10)
        {
            var data = await _context.Contracts
                .Include(c => c.Client)
                .GroupBy(c => c.Client)
                .Select(g => new
                {
                    ClientName = g.Key.Name,
                    TotalSales = g.Sum(c => c.TotalAmount),
                    ContractCount = g.Count()
                })
                .OrderByDescending(x => x.TotalSales)
                .Take(count)
                .ToListAsync();

            return data.Cast<object>();
        }
    }
}
