using BeyondSmartGlassTracker.Models;
using System.Windows.Media.Imaging;

namespace BeyondSmartGlassTracker.Services
{
    public interface IQRCodeService
    {
        BitmapImage GenerateQuotationQRCode(Quotation quotation);
        BitmapImage GenerateContractQRCode(Contract contract);
        BitmapImage GenerateQRCodeFromText(string text);
        Task<bool> SaveQRCodeAsImageAsync(BitmapImage qrCode, string fileName);
        string GenerateQuotationQRText(Quotation quotation);
        string GenerateContractQRText(Contract contract);
    }
}
