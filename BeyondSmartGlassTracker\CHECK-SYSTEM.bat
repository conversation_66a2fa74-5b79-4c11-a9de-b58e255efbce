@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - فحص النظام

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                        فحص النظام                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري فحص متطلبات النظام...
echo.

REM Check Windows version
echo 🖥️  نظام التشغيل:
ver
echo.

REM Check .NET installation
echo 🔧 فحص .NET:
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET مثبت - الإصدار:
    dotnet --version
    echo.
    
    echo 📋 معلومات .NET التفصيلية:
    dotnet --info
) else (
    echo ❌ .NET غير مثبت
    echo.
    echo 📥 يرجى تحميل .NET من:
    echo https://dotnet.microsoft.com/download/dotnet/
)

echo.
echo 💾 فحص المساحة المتاحة:
dir /-c | find "bytes free"

echo.
echo 🧠 فحص الذاكرة:
wmic computersystem get TotalPhysicalMemory /format:value | find "TotalPhysicalMemory"

echo.
echo 🔌 فحص الاتصال بالإنترنت:
ping -n 1 google.com >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الاتصال بالإنترنت متوفر
) else (
    echo ❌ لا يوجد اتصال بالإنترنت
    echo ⚠️  قد تحتاج للاتصال لتحميل المكونات
)

echo.
echo 📁 فحص ملفات البرنامج:
if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker.csproj" (
    echo ✅ ملفات البرنامج موجودة
) else (
    echo ❌ ملفات البرنامج مفقودة
)

echo.
echo 🔐 فحص الصلاحيات:
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ يتم التشغيل بصلاحيات المدير
) else (
    echo ⚠️  يتم التشغيل بصلاحيات المستخدم العادي
    echo 💡 قد تحتاج لتشغيل البرنامج كمدير في بعض الحالات
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ملخص الفحص                           ║
echo ╚══════════════════════════════════════════════════════════════╝

REM Final recommendation
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    if exist "BeyondSmartGlassTracker\BeyondSmartGlassTracker.csproj" (
        echo ✅ النظام جاهز لتشغيل البرنامج
        echo.
        echo 🚀 لتشغيل البرنامج، استخدم أحد الملفات التالية:
        echo    - RUN-PROGRAM.bat (الموصى به)
        echo    - QUICK-START.bat (تشغيل سريع)
        echo    - START.bat (تشغيل كامل)
    ) else (
        echo ❌ ملفات البرنامج مفقودة
        echo يرجى التأكد من وجود مجلد BeyondSmartGlassTracker
    )
) else (
    echo ❌ يجب تثبيت .NET أولاً
    echo.
    echo 📥 خطوات التثبيت:
    echo 1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/
    echo 2. حمل ".NET Desktop Runtime"
    echo 3. قم بتثبيته
    echo 4. أعد تشغيل هذا الفحص
)

echo.
pause
