using BeyondSmartGlassTracker.Services;
using System.Windows.Input;
using System.Windows.Media.Imaging;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class QRCodeDisplayViewModel : BaseViewModel
    {
        private readonly IQRCodeService _qrCodeService;
        private readonly IPrintService _printService;
        
        private string _title = "QR Code";
        private string _description = "Scan to view details";
        private string _qrCodeData = string.Empty;
        private BitmapImage? _qrCodeImage;
        private int _qrCodeSize = 150;

        public QRCodeDisplayViewModel(IQRCodeService qrCodeService, IPrintService printService)
        {
            _qrCodeService = qrCodeService;
            _printService = printService;
            
            SaveQRCodeCommand = new RelayCommand(async () => await SaveQRCode());
            PrintQRCodeCommand = new RelayCommand(async () => await PrintQRCode());
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public string QRCodeData
        {
            get => _qrCodeData;
            set => SetProperty(ref _qrCodeData, value);
        }

        public BitmapImage? QRCodeImage
        {
            get => _qrCodeImage;
            set => SetProperty(ref _qrCodeImage, value);
        }

        public int QRCodeSize
        {
            get => _qrCodeSize;
            set => SetProperty(ref _qrCodeSize, value);
        }

        public ICommand SaveQRCodeCommand { get; }
        public ICommand PrintQRCodeCommand { get; }

        public void GenerateQRCode(string data, string title, string description)
        {
            QRCodeData = data;
            Title = title;
            Description = description;
            QRCodeImage = _qrCodeService.GenerateQRCodeFromText(data);
        }

        private async Task SaveQRCode()
        {
            if (QRCodeImage != null)
            {
                var fileName = Title.Replace(" ", "_").Replace("#", "");
                var success = await _qrCodeService.SaveQRCodeAsImageAsync(QRCodeImage, fileName);
                
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("QR Code saved successfully");
                    // You could show a success message to the user here
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Failed to save QR Code");
                    // You could show an error message to the user here
                }
            }
        }

        private async Task PrintQRCode()
        {
            if (QRCodeImage != null)
            {
                System.Diagnostics.Debug.WriteLine("Print QR Code functionality will be implemented");
                // You could implement QR code printing here
            }
        }
    }
}
