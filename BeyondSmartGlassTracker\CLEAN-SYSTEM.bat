@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker - تنظيف النظام

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                        تنظيف النظام                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🧹 جاري تنظيف ملفات النظام المؤقتة...
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

echo 📁 تنظيف ملفات البناء...

REM Clean build artifacts
if exist "bin" (
    echo    - حذف مجلد bin
    rmdir /s /q "bin" 2>nul
)

if exist "obj" (
    echo    - حذف مجلد obj
    rmdir /s /q "obj" 2>nul
)

REM Clean temporary files
echo.
echo 🗑️  تنظيف الملفات المؤقتة...

for /r . %%f in (*.tmp) do (
    if exist "%%f" (
        echo    - حذف %%f
        del "%%f" 2>nul
    )
)

for /r . %%f in (*.log) do (
    if exist "%%f" (
        echo    - حذف %%f
        del "%%f" 2>nul
    )
)

for /r . %%f in (*.cache) do (
    if exist "%%f" (
        echo    - حذف %%f
        del "%%f" 2>nul
    )
)

REM Clean NuGet cache
echo.
echo 📦 تنظيف ذاكرة التخزين المؤقت للحزم...
dotnet nuget locals all --clear >nul 2>&1

REM Clean solution
echo.
echo 🔨 تنظيف المشروع...
dotnet clean >nul 2>&1

echo.
echo ✅ تم تنظيف النظام بنجاح
echo.

echo 💡 ما تم تنظيفه:
echo    ✓ ملفات البناء (bin, obj)
echo    ✓ الملفات المؤقتة (*.tmp, *.log, *.cache)
echo    ✓ ذاكرة التخزين المؤقت للحزم
echo    ✓ ملفات المشروع المؤقتة
echo.

echo 🔄 لإعادة بناء البرنامج، استخدم أحد الملفات التالية:
echo    - RUN-PROGRAM.bat
echo    - START.bat
echo    - UPDATE-PROGRAM.bat
echo.

echo 📊 المساحة المحررة:
REM Calculate freed space (approximate)
echo    تم تحرير مساحة تقديرية من القرص
echo.

pause
