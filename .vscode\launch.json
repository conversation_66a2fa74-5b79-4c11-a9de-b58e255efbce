{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Beyond Smart Glass Tracker",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/bin/Debug/net8.0-windows/BeyondSmartGlassTracker.exe",
            "args": [],
            "cwd": "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Launch Beyond Smart Glass Tracker (Release)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-release",
            "program": "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker/bin/Release/net8.0-windows/BeyondSmartGlassTracker.exe",
            "args": [],
            "cwd": "${workspaceFolder}/BeyondSmartGlassTracker/BeyondSmartGlassTracker",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "PowerShell: Launch Current File",
            "type": "PowerShell",
            "request": "launch",
            "script": "${file}",
            "args": []
        }
    ]
}