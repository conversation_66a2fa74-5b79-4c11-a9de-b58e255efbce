@echo off
chcp 65001 >nul

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ╚══════════════════════════════════════════════════════════════╝

echo 🚀 تشغيل سريع للبرنامج...

cd BeyondSmartGlassTracker

REM Quick check and run
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET مطلوب - يرجى تثبيته من: https://dotnet.microsoft.com/download/dotnet/
    pause
    exit /b 1
)

echo ✅ جاري التشغيل...
dotnet run --configuration Release

echo ✨ تم إغلاق البرنامج
echo 📋 بيانات الدخول: admin/admin123 أو sales1/sales123
pause
