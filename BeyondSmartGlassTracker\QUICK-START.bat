@echo off
chcp 65001 >nul

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ╚══════════════════════════════════════════════════════════════╝

echo 🚀 تشغيل سريع للبرنامج...

cd BeyondSmartGlassTracker

REM Quick check and run
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 8 مطلوب - يرجى تثبيته من: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo ✅ جاري التشغيل...
start /min dotnet run --configuration Release

echo ✨ تم تشغيل البرنامج في الخلفية
echo 📋 بيانات الدخول: admin/admin123 أو sales1/sales123
timeout /t 3 >nul
