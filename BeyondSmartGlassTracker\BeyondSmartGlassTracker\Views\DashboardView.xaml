<UserControl x:Class="BeyondSmartGlassTracker.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{StaticResource LightGrayBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Welcome Section -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="Welcome back!"
                          Style="{StaticResource HeaderText}"/>
                <TextBlock Text="Here's what's happening with your smart glass projects today."
                          Style="{StaticResource BodyText}"/>
            </StackPanel>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="1" Style="{StaticResource ModernCard}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Quick Actions"
                              Style="{StaticResource SectionHeaderText}"
                              Margin="0,0,0,15"/>
                    
                    <UniformGrid Columns="3" HorizontalAlignment="Stretch">
                        <Button Style="{StaticResource ModernButton}"
                               Margin="0,0,10,0"
                               Height="50"
                               Command="{Binding AddNewProjectCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Add New Project" Margin="10,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource SuccessButton}"
                               Margin="5,0"
                               Height="50"
                               Command="{Binding AddMaintenanceRequestCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Wrench" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Add Maintenance Request" Margin="10,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource WarningButton}"
                               Margin="10,0,0,0"
                               Height="50"
                               Command="{Binding AddNewVisitCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Calendar" Width="20" Height="20" VerticalAlignment="Center"/>
                                <TextBlock Text="Add New Visit" Margin="10,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Statistics Cards -->
            <Grid Grid.Row="2" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Projects -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource PrimaryStatCard}">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="FolderMultiple" 
                                               Width="40" 
                                               Height="40" 
                                               Foreground="White"
                                               HorizontalAlignment="Left"/>
                        <TextBlock Text="{Binding TotalProjects}"
                                  FontSize="32"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  Margin="0,10,0,0"/>
                        <TextBlock Text="Total Projects"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.9"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Completed Projects -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource SuccessStatCard}">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="CheckCircle" 
                                               Width="40" 
                                               Height="40" 
                                               Foreground="White"
                                               HorizontalAlignment="Left"/>
                        <TextBlock Text="{Binding CompletedProjects}"
                                  FontSize="32"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  Margin="0,10,0,0"/>
                        <TextBlock Text="Completed"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.9"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- In Progress Projects -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource WarningStatCard}">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="ClockOutline" 
                                               Width="40" 
                                               Height="40" 
                                               Foreground="White"
                                               HorizontalAlignment="Left"/>
                        <TextBlock Text="{Binding InProgressProjects}"
                                  FontSize="32"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  Margin="0,10,0,0"/>
                        <TextBlock Text="In Progress"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.9"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- This Month Completed -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource StatCard}">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="TrendingUp" 
                                               Width="40" 
                                               Height="40" 
                                               Foreground="{StaticResource PrimaryBlueBrush}"
                                               HorizontalAlignment="Left"/>
                        <TextBlock Text="{Binding ThisMonthCompleted}"
                                  FontSize="32"
                                  FontWeight="Bold"
                                  Foreground="{StaticResource PrimaryBlueBrush}"
                                  Margin="0,10,0,0"/>
                        <TextBlock Text="Completed During Month"
                                  FontSize="14"
                                  Foreground="{StaticResource DarkGrayBrush}"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Data Filters and Recent Activity -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Data Filters -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource ModernCard}" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="Data Filters"
                                  Style="{StaticResource SectionHeaderText}"
                                  Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Time Range -->
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Time Range" Style="{StaticResource SmallText}" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                <ComboBoxItem Content="Last Month" IsSelected="True"/>
                                <ComboBoxItem Content="All Cities"/>
                            </ComboBox>

                            <!-- City -->
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="City" Style="{StaticResource SmallText}" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" Margin="0,0,10,0">
                                <ComboBoxItem Content="All Cities" IsSelected="True"/>
                            </ComboBox>

                            <!-- Project Status -->
                            <TextBlock Grid.Row="0" Grid.Column="2" Text="Project Status" Style="{StaticResource SmallText}" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="1" Grid.Column="2" Margin="0,0,10,0">
                                <ComboBoxItem Content="All Statuses" IsSelected="True"/>
                            </ComboBox>

                            <!-- Material Type -->
                            <TextBlock Grid.Row="0" Grid.Column="3" Text="Material Type" Style="{StaticResource SmallText}" Margin="0,0,0,5"/>
                            <ComboBox Grid.Row="1" Grid.Column="3">
                                <ComboBoxItem Content="All Materials" IsSelected="True"/>
                            </ComboBox>

                            <!-- Apply Filters Button -->
                            <Button Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2"
                                   Style="{StaticResource ModernButton}"
                                   Content="Apply Filters"
                                   Margin="0,15,10,0"
                                   Command="{Binding ApplyFiltersCommand}"/>

                            <!-- Reset Button -->
                            <Button Grid.Row="2" Grid.Column="2" Grid.ColumnSpan="2"
                                   Style="{StaticResource SecondaryButton}"
                                   Content="Reset"
                                   Margin="0,15,0,0"
                                   Command="{Binding ResetFiltersCommand}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Recent Activity -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource ModernCard}" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="Recent Activity"
                                  Style="{StaticResource SectionHeaderText}"
                                  Margin="0,0,0,15"/>
                        
                        <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding RecentActivities}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="{StaticResource LightBlueBrush}" 
                                               BorderThickness="0,0,0,1" 
                                               Padding="0,10">
                                            <StackPanel>
                                                <TextBlock Text="{Binding Title}" 
                                                          Style="{StaticResource SmallText}"
                                                          FontWeight="SemiBold"/>
                                                <TextBlock Text="{Binding Description}" 
                                                          Style="{StaticResource CaptionText}"
                                                          Margin="0,2,0,0"/>
                                                <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:MMM dd, HH:mm}'}" 
                                                          Style="{StaticResource CaptionText}"
                                                          Margin="0,2,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
