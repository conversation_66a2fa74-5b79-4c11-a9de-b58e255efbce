using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlassTracker.Models
{
    public class Installation
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string InstallationNumber { get; set; } = string.Empty;

        public int ContractId { get; set; }
        public virtual Contract Contract { get; set; } = null!;

        public int? TeamId { get; set; }
        public virtual InstallationTeam? Team { get; set; }

        public InstallationStatus Status { get; set; } = InstallationStatus.Scheduled;

        public DateTime ScheduledDate { get; set; }

        public DateTime? StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        [StringLength(500)]
        public string? ClientFeedback { get; set; }

        public int? Rating { get; set; } // 1-5 stars

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class InstallationTeam
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual ICollection<InstallationTeamMember> Members { get; set; } = new List<InstallationTeamMember>();
        public virtual ICollection<Installation> Installations { get; set; } = new List<Installation>();
    }

    public class InstallationTeamMember
    {
        public int Id { get; set; }

        public int TeamId { get; set; }
        public virtual InstallationTeam Team { get; set; } = null!;

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(100)]
        public string? Role { get; set; }

        public bool IsActive { get; set; } = true;
    }

    public enum InstallationStatus
    {
        Scheduled = 1,
        InProgress = 2,
        Completed = 3,
        Cancelled = 4,
        Postponed = 5
    }
}
