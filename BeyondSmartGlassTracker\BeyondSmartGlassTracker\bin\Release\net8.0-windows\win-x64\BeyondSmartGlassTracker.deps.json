{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/win-x64": {"BeyondSmartGlassTracker/1.0.0": {"dependencies": {"EPPlus": "7.0.0", "LiveCharts.Wpf": "0.9.7", "MaterialDesignColors": "2.1.4", "MaterialDesignThemes": "4.9.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "QRCoder": "1.4.3", "iTextSharp": "********", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "8.0.16", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "8.0.16"}, "runtime": {"BeyondSmartGlassTracker.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.16": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1625.21506"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "8.0.1625.21506"}, "clretwrc.dll": {"fileVersion": "8.0.1625.21506"}, "clrgc.dll": {"fileVersion": "8.0.1625.21506"}, "clrjit.dll": {"fileVersion": "8.0.1625.21506"}, "coreclr.dll": {"fileVersion": "8.0.1625.21506"}, "createdump.exe": {"fileVersion": "8.0.1625.21506"}, "hostfxr.dll": {"fileVersion": "8.0.1625.21506"}, "hostpolicy.dll": {"fileVersion": "8.0.1625.21506"}, "mscordaccore.dll": {"fileVersion": "8.0.1625.21506"}, "mscordaccore_amd64_amd64_8.0.1625.21506.dll": {"fileVersion": "8.0.1625.21506"}, "mscordbi.dll": {"fileVersion": "8.0.1625.21506"}, "mscorrc.dll": {"fileVersion": "8.0.1625.21506"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.16": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1625.21702"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21506"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21702"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1625.21703"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "8.0.1625.21703"}, "PresentationNative_cor3.dll": {"fileVersion": "8.0.25.16802"}, "vcruntime140_cor3.dll": {"fileVersion": "14.44.34823.2"}, "wpfgfx_cor3.dll": {"fileVersion": "8.0.1625.21703"}}}, "BouncyCastle/1.8.9": {"runtime": {"lib/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.9.0", "fileVersion": "1.8.20343.1"}}}, "EPPlus/7.0.0": {"dependencies": {"EPPlus.Interfaces": "6.1.1", "EPPlus.System.Drawing": "6.1.1", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2"}, "runtime": {"lib/net7.0/EPPlus.dll": {"assemblyVersion": "7.0.0.3", "fileVersion": "7.0.0.3"}}}, "EPPlus.Interfaces/6.1.1": {"runtime": {"lib/net7.0/EPPlus.Interfaces.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "EPPlus.System.Drawing/6.1.1": {"dependencies": {"EPPlus.Interfaces": "6.1.1"}, "runtime": {"lib/net7.0/EPPlus.System.Drawing.dll": {"assemblyVersion": "6.1.1.0", "fileVersion": "6.1.1.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "iTextSharp/********": {"dependencies": {"BouncyCastle": "1.8.9"}, "runtime": {"lib/itextsharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "LiveCharts/0.9.7": {"runtime": {"lib/net45/LiveCharts.dll": {"assemblyVersion": "0.9.7.0", "fileVersion": "0.9.7.0"}}}, "LiveCharts.Wpf/0.9.7": {"dependencies": {"LiveCharts": "0.9.7"}, "runtime": {"lib/net45/LiveCharts.Wpf.dll": {"assemblyVersion": "0.9.7.0", "fileVersion": "0.9.7.0"}}}, "MaterialDesignColors/2.1.4": {"runtime": {"lib/net7.0/MaterialDesignColors.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.0"}}}, "MaterialDesignThemes/4.9.0": {"dependencies": {"MaterialDesignColors": "2.1.4", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"assemblyVersion": "4.9.0.0", "fileVersion": "4.9.0.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Common/4.5.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Data.Sqlite.Core/8.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Sqlite/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "8.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"runtime": {"lib/net5.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "2.3.2.0", "fileVersion": "2.3.2.0"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.39.4716"}}}, "Mono.TextTemplating/2.2.1": {"runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "QRCoder/1.4.3": {"runtime": {"lib/net6.0-windows7.0/QRCoder.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.core/2.1.6": {"runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Pipelines/6.0.3": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}}}, "libraries": {"BeyondSmartGlassTracker/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/8.0.16": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/8.0.16": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "BouncyCastle/1.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-axnBgvdD5n+FnEG6efk/tfKuMFru7R/EoISH9zjh319yb3HD24TEHSAbNN/lTRT2ulOGRxDgOsCjkuk08iwWPg==", "path": "bouncycastle/1.8.9", "hashPath": "bouncycastle.1.8.9.nupkg.sha512"}, "EPPlus/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fqDPDAB0v+sU1m0VxDy4jBKhH77BoiaGjglnzflUVoHDL3Bnf8Xf/V8/dm3uYhMwChExnqFXxYscslu46seZQQ==", "path": "epplus/7.0.0", "hashPath": "epplus.7.0.0.nupkg.sha512"}, "EPPlus.Interfaces/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-y7dkrOoE1ZR9Vgy1Jf2rEIaTf3SHlUjYt01NklP+F5Qh7S2ruPbzTcpYLRWMeXiG8XL8h2jqX4CyIkFt3NQGZw==", "path": "epplus.interfaces/6.1.1", "hashPath": "epplus.interfaces.6.1.1.nupkg.sha512"}, "EPPlus.System.Drawing/6.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-lRF5gHYrmkHOOiLMI0t6q8zNYjUrzRgAM5BCXumv5xiqXko8fx3AWI+HCNZfhEqVFGOop+42KfR5GiUcCoyoMw==", "path": "epplus.system.drawing/6.1.1", "hashPath": "epplus.system.drawing.6.1.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "iTextSharp/********": {"type": "package", "serviceable": true, "sha512": "sha512-vtnMhTEJdZFCkLqaQLjD8aqTBIVKHPrSFuSXnxbLEJlvE/j/l88fvG9wtsejXTmhtErMo0lA9T2LdfdfbwplYw==", "path": "itextsharp/********", "hashPath": "itextsharp.********.nupkg.sha512"}, "LiveCharts/0.9.7": {"type": "package", "serviceable": true, "sha512": "sha512-p4xyBrUaNNfkWZSNsIle0r51cyVyFg0Yi0uBRZM5GQtdFiA/7guZHlffFyNUILIdux10Ch2hUaJpcZENVU9aaQ==", "path": "livecharts/0.9.7", "hashPath": "livecharts.0.9.7.nupkg.sha512"}, "LiveCharts.Wpf/0.9.7": {"type": "package", "serviceable": true, "sha512": "sha512-lv6YZoK0B4yWykMq5b9grZXtIbTj+OWu4sVN9GDpdgV6zffu64m4d5CW8KSjfSl9rB8VU649x1NZvhoU67hCDg==", "path": "livecharts.wpf/0.9.7", "hashPath": "livecharts.wpf.0.9.7.nupkg.sha512"}, "MaterialDesignColors/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-C4Oy+qkjMoMPoZKyqYdCnIYtK8c0OSIHmNP73Vgc69NjiUG093xTkE7W/Ks54cTDS7fmWOtUHfwISTVTtb/YKg==", "path": "materialdesigncolors/2.1.4", "hashPath": "materialdesigncolors.2.1.4.nupkg.sha512"}, "MaterialDesignThemes/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bp9Auw70j+9V7WsUMT4pc8ulVzfL0Eav/tyGgICDirxxhKJwhqtC/6PRkTUm+R1t9611xiDuk5pSUNdDV6vfOQ==", "path": "materialdesignthemes/4.9.0", "hashPath": "materialdesignthemes.4.9.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pujbzfszX7jAl7oTbHhqx7pxd9jibeyHHl8zy1gd55XMaKWjDtc5XhhNYwQnrwWYCInNdVoArbaaAvLgW7TwuA==", "path": "microsoft.data.sqlite.core/8.0.0", "hashPath": "microsoft.data.sqlite.core.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hd3l+6Wyo4GwFAWa8J87L1X1ypYsk3za1lIsaF3U4X/tUJof/QPkuFbdfAADhmNqvqppmUL04RbgFM2nl5A7rQ==", "path": "microsoft.entityframeworkcore.sqlite/8.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vtnf4SIenAR0fp4OGEb83Dgn37lSMQqt6952e0f/6u/HNO4KQBKYiFw9vWIW4f4nNApre39WioW+jqaIVk15Wg==", "path": "microsoft.entityframeworkcore.sqlite.core/8.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.core.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/2.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q==", "path": "microsoft.io.recyclablememorystream/2.3.2", "hashPath": "microsoft.io.recyclablememorystream.2.3.2.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "serviceable": true, "sha512": "sha512-8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "QRCoder/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-fWuFqjm8GTlEb2GqBl3Hi8HZZeZQwBSHxvRPtPjyNbT82H0ff0JwavKRBmMaXCno1Av6McPC8aJzri0Mj2w9Jw==", "path": "qrcoder/1.4.3", "hashPath": "qrcoder.1.4.3.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "path": "sqlitepclraw.core/2.1.6", "hashPath": "sqlitepclraw.core.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-2<PERSON>bJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}