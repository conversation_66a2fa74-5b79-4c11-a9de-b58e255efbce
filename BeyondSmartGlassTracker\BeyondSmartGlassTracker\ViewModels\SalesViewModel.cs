using BeyondSmartGlassTracker.Services;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class SalesViewModel : BaseViewModel
    {
        private readonly ISalesService _salesService;
        private readonly IExcelExportService _excelExportService;
        private readonly IPrintService _printService;

        public SalesViewModel(
            ISalesService salesService,
            IExcelExportService excelExportService,
            IPrintService printService)
        {
            _salesService = salesService;
            _excelExportService = excelExportService;
            _printService = printService;

            ExportToExcelCommand = new RelayCommand(async () => await ExportToExcel());
            PrintReportCommand = new RelayCommand(async () => await PrintReport());
            RefreshDataCommand = new RelayCommand(() => RefreshData());

            LoadSalesData();
        }

        public ICommand ExportToExcelCommand { get; }
        public ICommand PrintReportCommand { get; }
        public ICommand RefreshDataCommand { get; }

        private async void LoadSalesData()
        {
            try
            {
                // Load sales data here
                System.Diagnostics.Debug.WriteLine("Loading sales data...");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sales data: {ex.Message}");
            }
        }

        private void RefreshData()
        {
            LoadSalesData();
            System.Diagnostics.Debug.WriteLine("Sales data refreshed");
        }

        private async Task ExportToExcel()
        {
            try
            {
                // Get sales data for export
                var salesData = new { Message = "Sales data will be exported here" };
                var excelData = await _excelExportService.ExportSalesReportToExcelAsync(salesData);
                var success = await _excelExportService.SaveExcelFileAsync(excelData, "Sales_Report");

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Sales report exported to Excel successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Excel export was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error exporting to Excel: {ex.Message}");
            }
        }

        private async Task PrintReport()
        {
            try
            {
                var salesData = new { Message = "Sales data will be printed here" };
                var success = await _printService.PrintSalesReportAsync(salesData, "Sales Report");
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("Sales report printed successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Print was cancelled or failed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error printing report: {ex.Message}");
            }
        }
    }
}
