@echo off
title Beyond Smart Glass Tracker - Developer Mode

echo ========================================
echo Beyond Smart Glass Tracker
echo Developer Mode
echo ========================================
echo.

echo Checking .NET SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK not found
    pause
    exit /b 1
)

echo.
echo Navigating to project directory...
cd BeyondSmartGlassTracker

echo.
echo Restoring packages...
dotnet restore

echo.
echo Building in Debug mode...
dotnet build --configuration Debug

if %errorlevel% neq 0 (
    echo BUILD FAILED
    pause
    exit /b 1
)

echo.
echo BUILD SUCCESSFUL
echo.
echo Login Credentials:
echo Admin: admin / admin123
echo Sales: sales1 / sales123
echo.
echo Starting application in Debug mode...
echo.

dotnet run --configuration Debug

echo.
echo Application closed.
pause
