# Beyond Smart Glass Tracker - Developer Guide

## 🛠️ Development Environment Setup

### Prerequisites
- .NET 8 SDK
- Visual Studio 2022 or later (recommended) or VS Code
- Git for version control

### Getting Started
1. Clone the repository
2. Open `BeyondSmartGlassTracker.sln` in Visual Studio
3. Restore NuGet packages: `dotnet restore`
4. Build the solution: `dotnet build`
5. Run the application: `dotnet run`

## 📁 Project Structure

```
BeyondSmartGlassTracker/
├── BeyondSmartGlassTracker/          # Main application
│   ├── Controls/                     # Custom WPF controls
│   │   └── QRCodeDisplayControl.xaml # QR code display control
│   ├── Data/                         # Database context and models
│   │   ├── AppDbContext.cs          # Entity Framework context
│   │   └── DatabaseSeeder.cs        # Initial data seeding
│   ├── Models/                       # Data models
│   │   ├── User.cs                  # User entity
│   │   ├── Client.cs                # Client entity
│   │   ├── Quotation.cs             # Quotation entity
│   │   ├── Contract.cs              # Contract entity
│   │   └── ...                      # Other entities
│   ├── Services/                     # Business logic services
│   │   ├── IAuthenticationService.cs # Authentication interface
│   │   ├── AuthenticationService.cs  # Authentication implementation
│   │   ├── IExcelExportService.cs   # Excel export interface
│   │   ├── ExcelExportService.cs    # Excel export implementation
│   │   ├── IQRCodeService.cs        # QR code interface
│   │   ├── QRCodeService.cs         # QR code implementation
│   │   ├── IPrintService.cs         # Print interface
│   │   ├── PrintService.cs          # Print implementation
│   │   └── ...                      # Other services
│   ├── ViewModels/                   # MVVM ViewModels
│   │   ├── BaseViewModel.cs         # Base ViewModel with RelayCommand
│   │   ├── LoginViewModel.cs        # Login logic
│   │   ├── MainViewModel.cs         # Main window logic
│   │   ├── QRCodeDisplayViewModel.cs # QR code display logic
│   │   └── ...                      # Other ViewModels
│   ├── Views/                        # WPF Views
│   │   ├── LoginWindow.xaml         # Login window
│   │   ├── MainWindow.xaml          # Main application window
│   │   ├── QRCodeWindow.xaml        # QR code display window
│   │   └── ...                      # Other views
│   ├── Styles/                       # WPF styles and resources
│   │   └── AppStyles.xaml           # Application styles
│   └── App.xaml                     # Application entry point
├── Scripts/                          # Batch files for easy execution
│   ├── RUN-PROGRAM.bat              # Main execution script
│   ├── QUICK-START.bat              # Quick start script
│   ├── DEV-RUN.bat                  # Developer execution
│   ├── CHECK-SYSTEM.bat             # System verification
│   ├── DIAGNOSE-ISSUES.bat          # Problem diagnosis
│   └── ...                          # Other utility scripts
└── README.md                        # Project documentation
```

## 🏗️ Architecture

### MVVM Pattern
The application follows the Model-View-ViewModel (MVVM) pattern:
- **Models**: Data entities and business objects
- **Views**: WPF user interface (XAML files)
- **ViewModels**: Presentation logic and data binding

### Dependency Injection
Services are registered in `App.xaml.cs` using Microsoft.Extensions.DependencyInjection:
```csharp
services.AddSingleton<IAuthenticationService, AuthenticationService>();
services.AddSingleton<IExcelExportService, ExcelExportService>();
services.AddSingleton<IQRCodeService, QRCodeService>();
services.AddSingleton<IPrintService, PrintService>();
```

### Database
- **Entity Framework Core** with SQLite
- Database file: `BeyondSmartGlass.db`
- Automatic migrations and seeding

## 🔧 Key Features Implementation

### QR Code Generation
- **Library**: QRCoder
- **Service**: `QRCodeService`
- **Control**: `QRCodeDisplayControl`
- **Features**: Generate, display, save, and print QR codes

### Excel Export
- **Library**: EPPlus
- **Service**: `ExcelExportService`
- **Features**: Export all data types with professional formatting

### Print Reports
- **Service**: `PrintService`
- **Features**: Print filtered reports with QR codes

### PDF Export
- **Library**: iTextSharp
- **Service**: `PdfExportService`
- **Features**: Generate professional PDF documents

## 🧪 Testing

### Running Tests
```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Test Structure
- Unit tests for services
- Integration tests for database operations
- UI tests for critical workflows

## 📦 Building and Deployment

### Development Build
```bash
dotnet build --configuration Debug
```

### Release Build
```bash
dotnet build --configuration Release
```

### Create Executable
```bash
# Use the provided script
./build-exe.bat

# Or manually
dotnet publish --configuration Release --runtime win-x64 --self-contained true -p:PublishSingleFile=true
```

## 🔍 Debugging

### Common Issues
1. **Database not found**: Run the application once to create the database
2. **Permission errors**: Run as administrator
3. **Missing dependencies**: Run `dotnet restore`

### Debug Configuration
- Set breakpoints in ViewModels and Services
- Use Debug output for logging
- Check database state with SQLite browser

## 🚀 Adding New Features

### Adding a New Service
1. Create interface in `Services/` folder
2. Implement the service class
3. Register in `App.xaml.cs`
4. Inject into ViewModels as needed

### Adding a New View
1. Create XAML file in `Views/` folder
2. Create corresponding ViewModel
3. Register ViewModel in DI container
4. Add navigation logic

### Adding a New Model
1. Create model class in `Models/` folder
2. Add DbSet to `AppDbContext`
3. Create migration: `dotnet ef migrations add NewModel`
4. Update database: `dotnet ef database update`

## 📋 Code Standards

### Naming Conventions
- **Classes**: PascalCase
- **Methods**: PascalCase
- **Properties**: PascalCase
- **Fields**: camelCase with underscore prefix
- **Constants**: UPPER_CASE

### Code Organization
- One class per file
- Group related functionality in folders
- Use meaningful names
- Add XML documentation for public APIs

### XAML Guidelines
- Use data binding instead of code-behind
- Follow Material Design principles
- Use consistent styling
- Implement proper accessibility

## 🔄 Version Control

### Branching Strategy
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: New features
- `bugfix/*`: Bug fixes

### Commit Messages
- Use conventional commits format
- Include issue numbers when applicable
- Write clear, descriptive messages

## 📚 Resources

### Documentation
- [.NET 8 Documentation](https://docs.microsoft.com/en-us/dotnet/)
- [WPF Documentation](https://docs.microsoft.com/en-us/dotnet/desktop/wpf/)
- [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/)

### Libraries Used
- **MaterialDesignInXamlToolkit**: UI components
- **EPPlus**: Excel export
- **QRCoder**: QR code generation
- **iTextSharp**: PDF generation
- **LiveCharts**: Data visualization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📞 Support

For development questions or issues:
- Check existing issues in the repository
- Create a new issue with detailed description
- Include system information and error logs
- Provide steps to reproduce the problem
