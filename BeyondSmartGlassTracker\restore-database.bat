@echo off
chcp 65001 >nul
title استعادة قاعدة البيانات

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    استعادة قاعدة البيانات                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "DB_FILE=BeyondSmartGlassTracker\BeyondSmartGlass.db"
set "BACKUP_DIR=Backups"

REM Check if backup directory exists
if not exist "%BACKUP_DIR%" (
    echo ❌ لا يوجد مجلد للنسخ الاحتياطية
    pause
    exit /b 1
)

echo 📁 النسخ الاحتياطية المتاحة:
echo.
dir /b "%BACKUP_DIR%\*.db" 2>nul
if %errorlevel% neq 0 (
    echo ❌ لا توجد نسخ احتياطية متاحة
    pause
    exit /b 1
)

echo.
echo ⚠️  تحذير: سيتم استبدال قاعدة البيانات الحالية
echo.
set /p "BACKUP_NAME=أدخل اسم ملف النسخة الاحتياطية (بدون المسار): "

set "BACKUP_FILE=%BACKUP_DIR%\%BACKUP_NAME%"

if not exist "%BACKUP_FILE%" (
    echo ❌ الملف المحدد غير موجود: %BACKUP_FILE%
    pause
    exit /b 1
)

echo.
set /p "CONFIRM=هل أنت متأكد من الاستعادة؟ (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo ❌ تم إلغاء العملية
    pause
    exit /b 0
)

REM Backup current database before restore
if exist "%DB_FILE%" (
    set "CURRENT_BACKUP=%BACKUP_DIR%\BeyondSmartGlass_BeforeRestore_%date:~-4,4%%date:~-10,2%%date:~-7,2%.db"
    copy "%DB_FILE%" "%CURRENT_BACKUP%" >nul
    echo ✅ تم حفظ نسخة احتياطية من قاعدة البيانات الحالية
)

REM Restore database
copy "%BACKUP_FILE%" "%DB_FILE%" >nul

if %errorlevel% equ 0 (
    echo ✅ تم استعادة قاعدة البيانات بنجاح
    echo 📁 من الملف: %BACKUP_FILE%
    echo.
    echo 🚀 يمكنك الآن تشغيل البرنامج
) else (
    echo ❌ فشل في استعادة قاعدة البيانات
)

echo.
pause
