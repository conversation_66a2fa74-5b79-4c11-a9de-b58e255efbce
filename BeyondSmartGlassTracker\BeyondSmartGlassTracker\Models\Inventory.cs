using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlassTracker.Models
{
    public class InventoryItem
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string SKU { get; set; } = string.Empty;

        public InventoryCategory Category { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        public int CurrentStock { get; set; }

        public int MinimumStock { get; set; }

        public int MaximumStock { get; set; }

        public decimal UnitCost { get; set; }

        [StringLength(50)]
        public string? Unit { get; set; } // sqm, pieces, etc.

        [StringLength(100)]
        public string? Supplier { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastUpdated { get; set; }

        // Navigation properties
        public virtual ICollection<InventoryTransaction> Transactions { get; set; } = new List<InventoryTransaction>();
    }

    public class InventoryTransaction
    {
        public int Id { get; set; }

        public int InventoryItemId { get; set; }
        public virtual InventoryItem InventoryItem { get; set; } = null!;

        public TransactionType Type { get; set; }

        public int Quantity { get; set; }

        public decimal UnitCost { get; set; }

        public decimal TotalCost { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        [StringLength(100)]
        public string? Reference { get; set; } // PO number, job number, etc.

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public int? UserId { get; set; }
        public virtual User? User { get; set; }
    }

    public enum InventoryCategory
    {
        SmartFilmRolls = 1,
        Devices = 2,
        Remotes = 3,
        Accessories = 4,
        Tools = 5,
        Other = 6
    }

    public enum TransactionType
    {
        Purchase = 1,
        Sale = 2,
        Adjustment = 3,
        Transfer = 4,
        Return = 5,
        Damage = 6
    }
}
