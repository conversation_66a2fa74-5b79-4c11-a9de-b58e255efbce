@echo off
chcp 65001 >nul
title Beyond Smart Glass Tracker

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Beyond Smart Glass Tracker                   ║
echo ║                      نظام إدارة المشاريع                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 جاري تشغيل البرنامج...
echo.

REM Check if .NET is available
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET 8 غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET 8 Desktop Runtime من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo ابحث عن ".NET Desktop Runtime 8.0.x" وقم بتثبيته
    echo.
    pause
    exit /b 1
)

echo ✅ .NET 8 متوفر
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

echo 📦 جاري تحضير المكونات...
dotnet restore --verbosity quiet

echo 🔨 جاري بناء البرنامج...
dotnet build --configuration Release --verbosity quiet

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء البرنامج
    pause
    exit /b 1
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    بيانات تسجيل الدخول                       ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ المدير العام:                                                ║
echo ║   اسم المستخدم: admin                                        ║
echo ║   كلمة المرور: admin123                                      ║
echo ║                                                              ║
echo ║ موظف المبيعات:                                               ║
echo ║   اسم المستخدم: sales1                                       ║
echo ║   كلمة المرور: sales123                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo ✨ جاري فتح البرنامج...
echo.

REM Run the application
dotnet run --configuration Release

echo.
echo 👋 تم إغلاق البرنامج
pause
