<Window x:Class="BeyondSmartGlassTracker.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Beyond Smart Glass Tracker - Login" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{StaticResource LightBlueBrush}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Login Card -->
        <materialDesign:Card Grid.Row="1" 
                           Margin="40"
                           Padding="30"
                           Background="{StaticResource WhiteBrush}"
                           materialDesign:ShadowAssist.ShadowDepth="Depth3">
            <StackPanel>
                <!-- Logo/Title -->
                <TextBlock Text="Beyond Smart Glass"
                          FontFamily="{StaticResource PrimaryFont}"
                          FontSize="24"
                          FontWeight="Bold"
                          Foreground="{StaticResource PrimaryBlueBrush}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"/>
                
                <TextBlock Text="Tracker"
                          FontFamily="{StaticResource PrimaryFont}"
                          FontSize="18"
                          Foreground="{StaticResource DarkGrayBrush}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,30"/>

                <!-- Username Field -->
                <TextBox x:Name="UsernameTextBox"
                        materialDesign:HintAssist.Hint="Username"
                        materialDesign:HintAssist.IsFloating="True"
                        FontFamily="{StaticResource PrimaryFont}"
                        FontSize="14"
                        Margin="0,0,0,20"
                        Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- Password Field -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="Password"
                           materialDesign:HintAssist.IsFloating="True"
                           FontFamily="{StaticResource PrimaryFont}"
                           FontSize="14"
                           Margin="0,0,0,20"/>

                <!-- Error Message -->
                <TextBlock x:Name="ErrorMessage"
                          Text="{Binding ErrorMessage}"
                          Foreground="{StaticResource ErrorBrush}"
                          FontFamily="{StaticResource PrimaryFont}"
                          FontSize="12"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,10"
                          Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                       Content="LOGIN"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Background="{StaticResource PrimaryBlueBrush}"
                       Foreground="White"
                       FontFamily="{StaticResource PrimaryFont}"
                       FontSize="14"
                       FontWeight="Bold"
                       Height="45"
                       Margin="0,10,0,0"
                       Command="{Binding LoginCommand}"
                       IsEnabled="{Binding CanLogin}">
                    <Button.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Button.Effect>
                </Button>

                <!-- Loading Indicator -->
                <ProgressBar x:Name="LoadingProgress"
                           IsIndeterminate="True"
                           Height="4"
                           Margin="0,15,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Foreground="{StaticResource PrimaryBlueBrush}"/>

                <!-- Version Info -->
                <TextBlock Text="Version 1.0.0"
                          FontFamily="{StaticResource PrimaryFont}"
                          FontSize="10"
                          Foreground="{StaticResource DarkGrayBrush}"
                          HorizontalAlignment="Center"
                          Margin="0,20,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
