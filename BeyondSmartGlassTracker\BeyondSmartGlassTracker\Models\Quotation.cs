using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlassTracker.Models
{
    public class Quotation
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string QuotationNumber { get; set; } = string.Empty;

        public int ClientId { get; set; }
        public virtual Client Client { get; set; } = null!;

        public int UserId { get; set; }
        public virtual User User { get; set; } = null!;

        public QuotationStatus Status { get; set; } = QuotationStatus.Draft;

        public decimal SubTotal { get; set; }

        public decimal DiscountPercentage { get; set; }

        public decimal DiscountAmount { get; set; }

        public decimal TotalAmount { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ApprovedAt { get; set; }

        public DateTime? ExpiresAt { get; set; }

        // Navigation properties
        public virtual ICollection<QuotationItem> Items { get; set; } = new List<QuotationItem>();
        public virtual Contract? Contract { get; set; }
    }

    public class QuotationItem
    {
        public int Id { get; set; }

        public int QuotationId { get; set; }
        public virtual Quotation Quotation { get; set; } = null!;

        public int ProductTypeId { get; set; }
        public virtual ProductType ProductType { get; set; } = null!;

        public decimal AreaSqm { get; set; }

        public int DeviceCount { get; set; }

        public decimal PricePerSqm { get; set; }

        public decimal TotalPrice { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }
    }

    public enum QuotationStatus
    {
        Draft = 1,
        Sent = 2,
        Approved = 3,
        Rejected = 4,
        Expired = 5
    }
}
