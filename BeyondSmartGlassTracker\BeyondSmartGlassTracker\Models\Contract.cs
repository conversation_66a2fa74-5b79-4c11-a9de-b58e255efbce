using System.ComponentModel.DataAnnotations;

namespace BeyondSmartGlassTracker.Models
{
    public class Contract
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string ContractNumber { get; set; } = string.Empty;

        public int QuotationId { get; set; }
        public virtual Quotation Quotation { get; set; } = null!;

        public int ClientId { get; set; }
        public virtual Client Client { get; set; } = null!;

        public int UserId { get; set; }
        public virtual User User { get; set; } = null!;

        public ContractStatus Status { get; set; } = ContractStatus.Active;

        public decimal TotalAmount { get; set; }

        public DateTime SignedAt { get; set; } = DateTime.UtcNow;

        public DateTime? StartDate { get; set; }

        public DateTime? CompletionDate { get; set; }

        [StringLength(1000)]
        public string? Terms { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<ContractItem> Items { get; set; } = new List<ContractItem>();
        public virtual ICollection<ManufacturingJob> ManufacturingJobs { get; set; } = new List<ManufacturingJob>();
        public virtual ICollection<Installation> Installations { get; set; } = new List<Installation>();
    }

    public class ContractItem
    {
        public int Id { get; set; }

        public int ContractId { get; set; }
        public virtual Contract Contract { get; set; } = null!;

        public int ProductTypeId { get; set; }
        public virtual ProductType ProductType { get; set; } = null!;

        public decimal AreaSqm { get; set; }

        public int DeviceCount { get; set; }

        public decimal PricePerSqm { get; set; }

        public decimal TotalPrice { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }
    }

    public enum ContractStatus
    {
        Active = 1,
        Completed = 2,
        Cancelled = 3,
        OnHold = 4
    }
}
