# Beyond Smart Glass Tracker
## نظام إدارة مشاريع الزجاج الذكي

A comprehensive WPF application for managing smart glass projects, built with .NET 8 and modern UI design principles.

## 🚀 Quick Start / التشغيل السريع

### For Users / للمستخدمين
1. **Double-click** `START.bat` to run the application
2. **انقر مرتين** على ملف `START.bat` لتشغيل البرنامج

### Login Credentials / بيانات الدخول
- **Admin**: `admin` / `admin123`
- **Sales**: `sales1` / `sales123`

## Features

### ✅ Completed Features

- **Authentication System**: Login with Admin/Sales user roles
- **Modern UI Design**: Clean, professional interface with Material Design elements
- **Database Integration**: SQLite database with Entity Framework Core
- **Navigation System**: Sidebar navigation with role-based access control
- **Dashboard**: Overview with statistics and recent activity
- **Excel Export**: Export data to Excel (.xlsx) format from all modules
- **PDF Export**: Generate PDF reports for quotations and contracts

### 🚧 Module Structure

1. **Dashboard** - Overview and quick actions
2. **Quotations** - Create and manage quotations
3. **Contracts** - Contract management from approved quotations
4. **Manufacturing** - Track manufacturing jobs and progress
5. **Installation** - Schedule and manage installations
6. **Inventory** - Manage smart glass components and materials
7. **Sales Dashboard** - Analytics and reporting
8. **Admin Panel** - User and system management (Admin only)

## Technology Stack

- **Framework**: .NET 8 WPF
- **Database**: SQLite with Entity Framework Core
- **UI Framework**: Material Design in XAML
- **Architecture**: MVVM Pattern
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **PDF Generation**: iTextSharp
- **Excel Export**: EPPlus
- **Charts**: LiveCharts.Wpf

## Getting Started

### Prerequisites

- .NET 8 SDK
- Visual Studio 2022 or later
- Windows 10/11

### Installation

1. Clone the repository
2. Open `BeyondSmartGlassTracker.sln` in Visual Studio
3. Restore NuGet packages
4. Build and run the application

### Default Login Credentials

- **Admin User**: 
  - Username: `admin`
  - Password: `admin123`
  
- **Sales User**:
  - Username: `sales1`
  - Password: `sales123`

## Database Schema

The application uses SQLite with the following main entities:

- **Users** - System users with role-based access
- **Clients** - Customer information
- **ProductTypes** - Smart glass product catalog
- **Quotations** - Price quotes for clients
- **Contracts** - Signed agreements
- **ManufacturingJobs** - Production tracking
- **Installations** - Installation scheduling
- **InventoryItems** - Stock management
- **InstallationTeams** - Installation crew management

## Project Structure

```
BeyondSmartGlassTracker/
├── Models/              # Data models and entities
├── Views/               # XAML user interface files
├── ViewModels/          # MVVM view models
├── Services/            # Business logic and data services
├── Data/                # Database context and configuration
├── Styles/              # UI styling and themes
├── Assets/              # Images, icons, and resources
└── Helpers/             # Utility classes and extensions
```

## Key Features

### Excel Export Functionality
- Export quotations, contracts, manufacturing jobs, installations, inventory, and client data
- Filtered export based on date ranges, status, city, etc.
- Professional formatting with headers and styling
- Automatic file naming with timestamps

### Modern UI Design
- Clean, professional appearance with white/light blue color scheme
- Rounded buttons with smooth hover animations
- Segoe UI font family throughout
- Material Design icons and components
- Responsive layout with proper spacing

### Role-Based Access Control
- Admin users have access to all modules including user management
- Sales users have access to quotations, contracts, and client management
- Secure authentication with password hashing

## Development Status

This is the initial framework with core functionality implemented. Individual modules (Quotations, Contracts, Manufacturing, etc.) are set up with placeholder views and can be expanded with full CRUD operations as needed.

## Future Enhancements

- Complete implementation of all CRUD operations for each module
- Advanced reporting and analytics
- Email integration for quotations and contracts
- Document management system
- Mobile app companion
- API for third-party integrations
- Advanced inventory tracking with barcode scanning
- Customer portal for project tracking

## Contributing

This project follows standard .NET development practices with MVVM architecture. When adding new features:

1. Create appropriate models in the `Models` folder
2. Add database configurations in `Data/AppDbContext.cs`
3. Implement services in the `Services` folder
4. Create ViewModels following the existing pattern
5. Design Views using the established styling conventions

## License

This project is proprietary software for Beyond Smart Glass company.
