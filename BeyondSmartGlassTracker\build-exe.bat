@echo off
echo ========================================
echo Building Beyond Smart Glass Tracker
echo Single Executable File
echo ========================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed on this system.
    echo.
    echo Please install .NET SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/
    echo.
    pause
    exit /b 1
)

echo .NET SDK is installed. Building single executable...
echo.

REM Navigate to project directory
cd BeyondSmartGlassTracker

REM Clean previous builds
echo Cleaning previous builds...
dotnet clean

REM Restore packages
echo Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages.
    pause
    exit /b 1
)

REM Publish as single file executable
echo Publishing as single executable file...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishReadyToRun=true -o ..\Release

if %errorlevel% neq 0 (
    echo ERROR: Publish failed.
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo The executable file has been created in the 'Release' folder:
echo BeyondSmartGlassTracker.exe
echo.
echo This file includes all dependencies and can run on any Windows 10/11 system
echo without requiring .NET to be installed separately.
echo.
echo File size: Approximately 150-200 MB (includes .NET runtime)
echo.
pause

REM Open the Release folder
start ..\Release
