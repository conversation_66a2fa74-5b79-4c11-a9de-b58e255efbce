<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern Rounded Button Style -->
    <Style x:Key="ModernButton" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontFamily" Value="{StaticResource PrimaryFont}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#5BA0F2"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.4"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#3A7BC8"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#CCCCCC"/>
                            <Setter Property="Foreground" Value="#666666"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="{StaticResource LightBlueBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBlueBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBlueBrush}"/>
    </Style>

    <!-- Success Button Style -->
    <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- Warning Button Style -->
    <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
    </Style>

    <!-- Error Button Style -->
    <Style x:Key="ErrorButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
    </Style>

    <!-- Icon Button Style -->
    <Style x:Key="IconButton" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource LightBlueBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="{StaticResource PrimaryBlueBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
