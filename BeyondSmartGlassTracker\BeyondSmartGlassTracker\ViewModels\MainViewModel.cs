using BeyondSmartGlassTracker.Models;
using BeyondSmartGlassTracker.Services;
using BeyondSmartGlassTracker.Views;
using System.Windows.Controls;
using System.Windows.Input;

namespace BeyondSmartGlassTracker.ViewModels
{
    public class MainViewModel : BaseViewModel
    {
        private readonly IAuthenticationService _authService;
        private UserControl? _currentView;
        private string _currentPageTitle = "Dashboard";

        public MainViewModel(IAuthenticationService authService)
        {
            _authService = authService;
            NavigateCommand = new RelayCommand<string>(Navigate);
            LogoutCommand = new RelayCommand(async () => await LogoutAsync());
            
            // Set initial view
            Navigate("Dashboard");
        }

        public User? CurrentUser => _authService.CurrentUser;

        public bool IsAdmin => CurrentUser?.Role == UserRole.Admin;

        public UserControl? CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        public string CurrentPageTitle
        {
            get => _currentPageTitle;
            set => SetProperty(ref _currentPageTitle, value);
        }

        public ICommand NavigateCommand { get; }
        public ICommand LogoutCommand { get; }

        private void Navigate(string? page)
        {
            if (string.IsNullOrEmpty(page)) return;

            UserControl? view = page switch
            {
                "Dashboard" => new DashboardView(),
                "Quotations" => new QuotationsView(),
                "Contracts" => new ContractsView(),
                "Manufacturing" => new ManufacturingView(),
                "Installation" => new InstallationView(),
                "Inventory" => new InventoryView(),
                "Sales" => new SalesView(),
                "Admin" when IsAdmin => new AdminView(),
                _ => new DashboardView()
            };

            if (view != null)
            {
                CurrentView = view;
                CurrentPageTitle = page;
            }
        }

        private async Task LogoutAsync()
        {
            await _authService.LogoutAsync();
            
            var loginWindow = new LoginWindow();
            loginWindow.Show();
            
            // Close main window
            System.Windows.Application.Current.MainWindow?.Close();
        }
    }
}
